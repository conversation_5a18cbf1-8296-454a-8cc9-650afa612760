<Project Sdk="Microsoft.NET.Sdk">


    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>DongleKeyManager.Application.xml</DocumentationFile>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="applicationsettings.json"/>
        <None Remove="DongleKeyManager.Application.xml"/>
    </ItemGroup>

    <ItemGroup>
        <Content Include="applicationsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\DongleKeyManager.Core\DongleKeyManager.Core.csproj"/>
    </ItemGroup>

</Project>
