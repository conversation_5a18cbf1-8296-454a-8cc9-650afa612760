using DongleKeyManager.Application.DongleDevice.Dtos;
using DongleKeyManager.Application.User.Dtos;
using DongleKeyManager.Core.Entities;

namespace DongleKeyManager.Application;

public class Mapper : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // 设备实体映射配置
        config.NewConfig<Core.Entities.DongleDevice, DongleDeviceDto>()
            .Map(dest => dest.StatusDescription, src => GetDeviceStatusDescription(src.Status));

        config.NewConfig<CreateDongleDeviceDto, Core.Entities.DongleDevice>()
            .Map(dest => dest.CreatedTime, src => DateTime.Now)
            .Map(dest => dest.Status, src => DeviceStatus.Inactive);

        // 用户实体映射配置
        config.NewConfig<Core.Entities.User, UserDto>()
            .Map(dest => dest.RoleDescription, src => GetUserRoleDescription(src.Role))
            .Map(dest => dest.StatusDescription, src => GetUserStatusDescription(src.Status));

        config.NewConfig<CreateUserDto, Core.Entities.User>()
            .Map(dest => dest.CreatedTime, src => DateTime.Now)
            .Map(dest => dest.Status, src => UserStatus.Normal);
    }

    /// <summary>
    /// 获取设备状态描述
    /// </summary>
    private string GetDeviceStatusDescription(DeviceStatus status)
    {
        return status switch
        {
            DeviceStatus.Inactive => "未激活",
            DeviceStatus.Active => "已激活",
            DeviceStatus.Disabled => "已禁用",
            DeviceStatus.Expired => "已过期",
            _ => "未知状态"
        };
    }

    /// <summary>
    /// 获取用户角色描述
    /// </summary>
    private string GetUserRoleDescription(UserRole role)
    {
        return role switch
        {
            UserRole.User => "普通用户",
            UserRole.Admin => "管理员",
            UserRole.SuperAdmin => "超级管理员",
            _ => "未知角色"
        };
    }

    /// <summary>
    /// 获取用户状态描述
    /// </summary>
    private string GetUserStatusDescription(UserStatus status)
    {
        return status switch
        {
            UserStatus.Normal => "正常",
            UserStatus.Disabled => "已禁用",
            UserStatus.Locked => "已锁定",
            _ => "未知状态"
        };
    }
}