using DongleKeyManager.Application.System.Services;

namespace DongleKeyManager.Application.System;

/// <summary>
/// 系统管理API服务
/// </summary>
[ApiDescriptionSettings("System", Name = "System", Order = 0)]
public class SystemAppService : IDynamicA<PERSON><PERSON>ontroller, ITransient
{
    private readonly ISystemInitializationService _systemInitializationService;

    public SystemAppService( ISystemInitializationService systemInitializationService)
    {
        _systemInitializationService = systemInitializationService;
    }

    /// <summary>
    /// 初始化系统
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("initialize")]
    [ApiDescriptionSettings(Name = "Initialize")]
    [AllowAnonymous]
    public async Task<bool> InitializeAsync()
    {
        return await _systemInitializationService.InitializeSystemAsync();
    }

    /// <summary>
    /// 检查系统是否已初始化
    /// </summary>
    /// <returns>是否已初始化</returns>
    [HttpGet("initialized")]
    [ApiDescriptionSettings(Name = "IsInitialized")]
    [AllowAnonymous]
    public async Task<bool> IsInitializedAsync()
    {
        return await _systemInitializationService.IsSystemInitializedAsync();
    }

    /// <summary>
    /// 获取系统状态
    /// </summary>
    /// <returns>系统状态</returns>
    [HttpGet("status")]
    [ApiDescriptionSettings(Name = "GetStatus")]
    [AllowAnonymous]
    public async Task<SystemStatusDto> GetStatusAsync()
    {
        return await _systemInitializationService.GetSystemStatusAsync();
    }

    /// <summary>
    /// 重置系统（危险操作）
    /// </summary>
    /// <returns>重置结果</returns>
    [HttpPost("reset")]
    [ApiDescriptionSettings(Name = "Reset")]
    public async Task<bool> ResetAsync()
    {
        return await _systemInitializationService.ResetSystemAsync();
    }
}