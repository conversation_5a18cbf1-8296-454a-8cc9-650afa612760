using DongleKeyManager.Application.User.Services;
using DongleKeyManager.Core;
using DongleKeyManager.Core.Database;

namespace DongleKeyManager.Application.System.Services;

/// <summary>
/// 系统初始化服务实现
/// </summary>
public class SystemInitializationService : ISystemInitializationService, ITransient
{
    private readonly IUserService _userService;
    private static readonly DateTime _startupTime = DateTime.Now;

    public SystemInitializationService(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// 初始化系统
    /// </summary>
    public async Task<bool> InitializeSystemAsync()
    {
        try
        {
            // 初始化数据库
            await DatabaseInitializer.InitializeAsync();

            // 初始化默认管理员用户
            await _userService.InitializeDefaultAdminAsync();

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"系统初始化失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查系统是否已初始化
    /// </summary>
    public async Task<bool> IsSystemInitializedAsync()
    {
        try
        {
            // 检查数据库连接
            if (!await DatabaseInitializer.CheckConnectionAsync())
            {
                return false;
            }

            // 检查是否存在超级管理员
            var adminUser = await _userService.GetByUsernameAsync("admin");
            return adminUser != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取系统状态
    /// </summary>
    public async Task<SystemStatusDto> GetSystemStatusAsync()
    {
        var status = new SystemStatusDto
        {
            StartupTime = _startupTime,
            UptimeSeconds = (long)(DateTime.Now - _startupTime).TotalSeconds
        };

        try
        {
            // 检查系统初始化状态
            status.IsInitialized = await IsSystemInitializedAsync();

            // 检查数据库连接
            status.DatabaseConnected = await DatabaseInitializer.CheckConnectionAsync();

            if (status.DatabaseConnected)
            {
                // 获取数据库信息
                status.DatabaseInfo = await DatabaseInitializer.GetDatabaseInfoAsync();
            }
            else
            {
                status.Errors.Add("数据库连接失败");
            }

            // 检查系统配置
            await CheckSystemConfigurationAsync(status);
        }
        catch (Exception ex)
        {
            status.Errors.Add($"获取系统状态时发生错误: {ex.Message}");
        }

        return status;
    }

    /// <summary>
    /// 重置系统（危险操作）
    /// </summary>
    public async Task<bool> ResetSystemAsync()
    {
        try
        {
            // 这里可以实现系统重置逻辑
            // 注意：这是一个危险操作，应该有适当的权限控制
            
            // 清空所有表数据（保留表结构）
            DbContext.Instance.Deleteable<Core.Entities.DeviceLog>().ExecuteCommand();
            DbContext.Instance.Deleteable<Core.Entities.DongleDevice>().ExecuteCommand();
            DbContext.Instance.Deleteable<Core.Entities.User>().ExecuteCommand();

            // 重新初始化系统
            return await InitializeSystemAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"系统重置失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查系统配置
    /// </summary>
    private async Task CheckSystemConfigurationAsync(SystemStatusDto status)
    {
        try
        {
            // 检查必要的配置项
            var connectionConfigs = App.GetConfig<List<SqlSugar.ConnectionConfig>>("ConnectionConfigs");
            if (connectionConfigs == null || !connectionConfigs.Any())
            {
                status.Errors.Add("数据库连接配置缺失");
            }

            // 检查JWT配置（如果使用）
            try
            {
                var jwtSettings = App.GetConfig<object>("JwtSettings");
                if (jwtSettings == null)
                {
                    status.Warnings.Add("JWT配置缺失，可能影响用户认证功能");
                }
            }
            catch
            {
                status.Warnings.Add("JWT配置缺失，可能影响用户认证功能");
            }

            // 检查CORS配置
            try
            {
                var corsSettings = App.GetConfig<object>("CorsAccessorSettings");
                if (corsSettings == null)
                {
                    status.Warnings.Add("CORS配置缺失，可能影响跨域访问");
                }
            }
            catch
            {
                status.Warnings.Add("CORS配置缺失，可能影响跨域访问");
            }

            // 检查日志配置
            try
            {
                var loggingSettings = App.GetConfig<object>("Logging");
                if (loggingSettings == null)
                {
                    status.Warnings.Add("日志配置缺失");
                }
            }
            catch
            {
                status.Warnings.Add("日志配置缺失");
            }
        }
        catch (Exception ex)
        {
            status.Warnings.Add($"配置检查时发生错误: {ex.Message}");
        }
    }
}
