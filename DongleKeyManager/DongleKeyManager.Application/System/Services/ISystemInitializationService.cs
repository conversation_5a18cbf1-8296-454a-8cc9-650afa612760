using DongleKeyManager.Core.Database;

namespace DongleKeyManager.Application.System.Services;

/// <summary>
/// 系统初始化服务接口
/// </summary>
public interface ISystemInitializationService
{
    /// <summary>
    /// 初始化系统
    /// </summary>
    /// <returns>初始化结果</returns>
    Task<bool> InitializeSystemAsync();

    /// <summary>
    /// 检查系统是否已初始化
    /// </summary>
    /// <returns>是否已初始化</returns>
    Task<bool> IsSystemInitializedAsync();

    /// <summary>
    /// 获取系统状态
    /// </summary>
    /// <returns>系统状态</returns>
    Task<SystemStatusDto> GetSystemStatusAsync();

    /// <summary>
    /// 重置系统（危险操作）
    /// </summary>
    /// <returns>重置结果</returns>
    Task<bool> ResetSystemAsync();
}

/// <summary>
/// 系统状态DTO
/// </summary>
public class SystemStatusDto
{
    /// <summary>
    /// 系统是否已初始化
    /// </summary>
    public bool IsInitialized { get; set; }

    /// <summary>
    /// 数据库连接状态
    /// </summary>
    public bool DatabaseConnected { get; set; }

    /// <summary>
    /// 数据库信息
    /// </summary>
    public DatabaseInfo? DatabaseInfo { get; set; }

    /// <summary>
    /// 系统版本
    /// </summary>
    public string SystemVersion { get; set; } = "1.0.0";

    /// <summary>
    /// 启动时间
    /// </summary>
    public DateTime StartupTime { get; set; }

    /// <summary>
    /// 运行时间（秒）
    /// </summary>
    public long UptimeSeconds { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}
