using DongleKeyManager.Core;
using DongleKeyManager.Core.SeedData;

namespace DongleKeyManager.Application.SeedData;

/// <summary>
/// 种子数据管理API服务
/// </summary>
[ApiDescriptionSettings("SeedData", Name = "SeedData", Order = 10)]
public class SeedDataAppService : IDynamicApiController, ITransient
{
    private readonly ISeedDataService _seedDataService;

    public SeedDataAppService(ISeedDataService seedDataService)
    {
        _seedDataService = seedDataService;
    }

    /// <summary>
    /// 初始化所有种子数据
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("initialize-all")]
    [ApiDescriptionSettings(Name = "InitializeAll")]
    public async Task<string> InitializeAllAsync()
    {
        await _seedDataService.InitializeAllSeedDataAsync();
        return "种子数据初始化成功";
    }

    /// <summary>
    /// 初始化用户种子数据
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("initialize-users")]
    [ApiDescriptionSettings(Name = "InitializeUsers")]
    public async Task<string> InitializeUsersAsync()
    {
        await _seedDataService.InitializeUserSeedDataAsync();
        return "用户种子数据初始化成功";
    }

    /// <summary>
    /// 初始化设备种子数据
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("initialize-devices")]
    [ApiDescriptionSettings(Name = "InitializeDevices")]
    public async Task<string> InitializeDevicesAsync()
    {
        await _seedDataService.InitializeDeviceSeedDataAsync();
        return "设备种子数据初始化成功";
    }

    /// <summary>
    /// 清空所有数据
    /// </summary>
    /// <returns>清空结果</returns>
    [HttpPost("clear-all")]
    [ApiDescriptionSettings(Name = "ClearAll")]
    public async Task<string> ClearAllAsync()
    {
        await _seedDataService.ClearAllDataAsync();
        return "所有数据清空成功";
    }

    /// <summary>
    /// 重置为初始状态
    /// </summary>
    /// <returns>重置结果</returns>
    [HttpPost("reset")]
    [ApiDescriptionSettings(Name = "Reset")]
    public async Task<string> ResetAsync()
    {
        await _seedDataService.ResetToInitialStateAsync();
        return "系统重置为初始状态成功";
    }

    /// <summary>
    /// 获取数据统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    [ApiDescriptionSettings(Name = "GetStatistics")]
    [AllowAnonymous]
    public async Task<DataStatisticsDto> GetStatisticsAsync()
    {
        var userCount = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => !x.IsDeleted)
            .CountAsync();

        var deviceCount = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted)
            .CountAsync();

        var logCount = await DbContext.Instance.Queryable<Core.Entities.DeviceLog>()
            .CountAsync();

        var activeDeviceCount = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted && x.Status == Core.Entities.DeviceStatus.Active)
            .CountAsync();

        var inactiveDeviceCount = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted && x.Status == Core.Entities.DeviceStatus.Inactive)
            .CountAsync();

        var disabledDeviceCount = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted && x.Status == Core.Entities.DeviceStatus.Disabled)
            .CountAsync();

        var expiredDeviceCount = await DbContext.Instance.Queryable<Core.Entities.DongleDevice>()
            .Where(x => !x.IsDeleted && x.Status == Core.Entities.DeviceStatus.Expired)
            .CountAsync();

        return new DataStatisticsDto
        {
            UserCount = userCount,
            DeviceCount = deviceCount,
            LogCount = logCount,
            ActiveDeviceCount = activeDeviceCount,
            InactiveDeviceCount = inactiveDeviceCount,
            DisabledDeviceCount = disabledDeviceCount,
            ExpiredDeviceCount = expiredDeviceCount,
            LastUpdated = DateTime.Now
        };
    }
}



/// <summary>
/// 数据统计DTO
/// </summary>
public class DataStatisticsDto
{
    /// <summary>
    /// 用户总数
    /// </summary>
    public int UserCount { get; set; }

    /// <summary>
    /// 设备总数
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 日志总数
    /// </summary>
    public int LogCount { get; set; }

    /// <summary>
    /// 已激活设备数
    /// </summary>
    public int ActiveDeviceCount { get; set; }

    /// <summary>
    /// 未激活设备数
    /// </summary>
    public int InactiveDeviceCount { get; set; }

    /// <summary>
    /// 已禁用设备数
    /// </summary>
    public int DisabledDeviceCount { get; set; }

    /// <summary>
    /// 已过期设备数
    /// </summary>
    public int ExpiredDeviceCount { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }
}
