using System.Security.Cryptography;
using System.Text;

namespace DongleKeyManager.Application.Services;

/// <summary>
/// 加密服务实现
/// </summary>
public class EncryptionService : IEncryptionService, ITransient
{
    /// <summary>
    /// 生成MD5哈希
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>MD5哈希值</returns>
    public string GenerateMD5Hash(string input)
    {
        if (string.IsNullOrEmpty(input))
            return string.Empty;

        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(input);
        var hashBytes = md5.ComputeHash(inputBytes);

        var sb = new StringBuilder();
        foreach (var b in hashBytes)
        {
            sb.Append(b.ToString("x2"));
        }

        return sb.ToString();
    }

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <returns>密码哈希值</returns>
    public string GeneratePasswordHash(string password, string salt)
    {
        if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(salt))
            return string.Empty;

        var saltedPassword = password + salt;
        using var sha256 = SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(saltedPassword);
        var hashBytes = sha256.ComputeHash(inputBytes);

        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <param name="length">盐值长度</param>
    /// <returns>随机盐值</returns>
    public string GenerateSalt(int length = 16)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var salt = new char[length];

        for (int i = 0; i < length; i++)
        {
            salt[i] = chars[random.Next(chars.Length)];
        }

        return new string(salt);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">输入密码</param>
    /// <param name="hash">存储的哈希值</param>
    /// <param name="salt">盐值</param>
    /// <returns>验证结果</returns>
    public bool VerifyPassword(string password, string hash, string salt)
    {
        if (string.IsNullOrEmpty(password) || string.IsNullOrEmpty(hash) || string.IsNullOrEmpty(salt))
            return false;

        var computedHash = GeneratePasswordHash(password, salt);
        return computedHash.Equals(hash, StringComparison.Ordinal);
    }

    /// <summary>
    /// 基于硬件唯一标识号生成加密字符串
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <returns>加密字符串</returns>
    public string GenerateDeviceEncryptedString(string hardwareUniqueId)
    {
        if (string.IsNullOrEmpty(hardwareUniqueId))
            return string.Empty;

        // 使用硬件唯一标识号 + 固定盐值生成MD5哈希
        var saltedInput = hardwareUniqueId + "DongleKeyManager2024";
        return GenerateMD5Hash(saltedInput);
    }

    /// <summary>
    /// 验证设备加密字符串
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <param name="encryptedString">加密字符串</param>
    /// <returns>验证结果</returns>
    public bool VerifyDeviceEncryptedString(string hardwareUniqueId, string encryptedString)
    {
        if (string.IsNullOrEmpty(hardwareUniqueId) || string.IsNullOrEmpty(encryptedString))
            return false;

        var computedEncryptedString = GenerateDeviceEncryptedString(hardwareUniqueId);
        return computedEncryptedString.Equals(encryptedString, StringComparison.OrdinalIgnoreCase);
    }
}
