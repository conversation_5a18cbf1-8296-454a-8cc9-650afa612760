namespace DongleKeyManager.Application.Services;

/// <summary>
/// 加密服务接口
/// </summary>
public interface IEncryptionService
{
    /// <summary>
    /// 生成MD5哈希
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>MD5哈希值</returns>
    string GenerateMD5Hash(string input);

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    /// <param name="password">密码</param>
    /// <param name="salt">盐值</param>
    /// <returns>密码哈希值</returns>
    string GeneratePasswordHash(string password, string salt);

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    /// <param name="length">盐值长度</param>
    /// <returns>随机盐值</returns>
    string GenerateSalt(int length = 16);

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">输入密码</param>
    /// <param name="hash">存储的哈希值</param>
    /// <param name="salt">盐值</param>
    /// <returns>验证结果</returns>
    bool VerifyPassword(string password, string hash, string salt);

    /// <summary>
    /// 基于硬件唯一标识号生成加密字符串
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <returns>加密字符串</returns>
    string GenerateDeviceEncryptedString(string hardwareUniqueId);

    /// <summary>
    /// 验证设备加密字符串
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <param name="encryptedString">加密字符串</param>
    /// <returns>验证结果</returns>
    bool VerifyDeviceEncryptedString(string hardwareUniqueId, string encryptedString);
}
