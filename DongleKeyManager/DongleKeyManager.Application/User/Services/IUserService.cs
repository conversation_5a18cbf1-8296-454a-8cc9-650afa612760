using DongleKeyManager.Application.User.Dtos;

namespace DongleKeyManager.Application.User.Services;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="dto">登录DTO</param>
    /// <returns>登录结果</returns>
    Task<LoginResultDto> LoginAsync(LoginDto dto);

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="dto">创建用户DTO</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>用户信息</returns>
    Task<UserDto> CreateAsync(CreateUserDto dto, long? operatorUserId = null);

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="dto">更新用户DTO</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>用户信息</returns>
    Task<UserDto> UpdateAsync(UpdateUserDto dto, long? operatorUserId = null);

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteAsync(long id, long? operatorUserId = null);

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户信息</returns>
    Task<UserDto?> GetByIdAsync(long id);

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    Task<UserDto?> GetByUsernameAsync(string username);

    /// <summary>
    /// 分页查询用户
    /// </summary>
    /// <param name="dto">查询条件</param>
    /// <returns>分页结果</returns>
    Task<(List<UserDto> Items, int TotalCount)> GetPagedListAsync(QueryUserDto dto);

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="dto">修改密码DTO</param>
    /// <returns>修改结果</returns>
    Task<bool> ChangePasswordAsync(ChangePasswordDto dto);

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="newPassword">新密码</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>重置结果</returns>
    Task<bool> ResetPasswordAsync(long userId, string newPassword, long? operatorUserId = null);

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeId">排除的用户ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsUsernameExistsAsync(string username, long? excludeId = null);

    /// <summary>
    /// 启用用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>启用结果</returns>
    Task<bool> EnableAsync(long id, long? operatorUserId = null);

    /// <summary>
    /// 禁用用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>禁用结果</returns>
    Task<bool> DisableAsync(long id, long? operatorUserId = null);

    /// <summary>
    /// 锁定用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>锁定结果</returns>
    Task<bool> LockAsync(long id, long? operatorUserId = null);

    /// <summary>
    /// 解锁用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="operatorUserId">操作用户ID</param>
    /// <returns>解锁结果</returns>
    Task<bool> UnlockAsync(long id, long? operatorUserId = null);

    /// <summary>
    /// 更新最后登录时间
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateLastLoginTimeAsync(long userId);

    /// <summary>
    /// 初始化默认管理员用户
    /// </summary>
    /// <returns>初始化结果</returns>
    Task<bool> InitializeDefaultAdminAsync();
}
