using DongleKeyManager.Application.Services;
using DongleKeyManager.Application.User.Dtos;
using DongleKeyManager.Core;
using DongleKeyManager.Core.Entities;
using System.Text;

namespace DongleKeyManager.Application.User.Services;

/// <summary>
/// 用户服务实现
/// </summary>
public class UserService : IUserService, ITransient
{
    private readonly IEncryptionService _encryptionService;

    public UserService(IEncryptionService encryptionService)
    {
        _encryptionService = encryptionService;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    public async Task<LoginResultDto> LoginAsync(LoginDto dto)
    {
        var result = new LoginResultDto();

        try
        {
            // 根据用户名查找用户
            var user = await DbContext.Instance.Queryable<Core.Entities.User>()
                .Where(x => x.Username == dto.Username && !x.IsDeleted)
                .FirstAsync();

            if (user == null)
            {
                result.IsSuccess = false;
                result.Message = "用户名或密码错误";
                return result;
            }

            // 检查用户状态
            if (user.Status == UserStatus.Disabled)
            {
                result.IsSuccess = false;
                result.Message = "用户已被禁用";
                return result;
            }

            if (user.Status == UserStatus.Locked)
            {
                result.IsSuccess = false;
                result.Message = "用户已被锁定";
                return result;
            }

            // 验证密码
            if (!_encryptionService.VerifyPassword(dto.Password, user.PasswordHash, user.Salt))
            {
                result.IsSuccess = false;
                result.Message = "用户名或密码错误";
                return result;
            }

            // 更新最后登录时间和登录次数
            await UpdateLastLoginTimeAsync(user.Id);

            // 生成JWT令牌（这里简化处理，实际项目中应该使用JWT）
            var token = GenerateAccessToken(user);

            result.IsSuccess = true;
            result.AccessToken = token;
            result.TokenType = "Bearer";
            result.ExpiresIn = 7200; // 2小时
            result.User = await MapToDto(user);
            result.Message = "登录成功";

            return result;
        }
        catch
        {
            result.IsSuccess = false;
            result.Message = "登录过程中发生错误";
            throw;
        }
    }

    /// <summary>
    /// 创建用户
    /// </summary>
    public async Task<UserDto> CreateAsync(CreateUserDto dto, long? operatorUserId = null)
    {
        // 检查用户名是否已存在
        if (await IsUsernameExistsAsync(dto.Username))
        {
            throw Oops.Bah($"用户名 {dto.Username} 已存在");
        }

        // 生成盐值和密码哈希
        var salt = _encryptionService.GenerateSalt();
        var passwordHash = _encryptionService.GeneratePasswordHash(dto.Password, salt);

        var entity = new Core.Entities.User
        {
            Username = dto.Username,
            PasswordHash = passwordHash,
            Salt = salt,
            Role = dto.Role,
            Remarks = dto.Remarks,
            CreatedTime = DateTime.Now
        };

        var result = await DbContext.Instance.Insertable(entity).ExecuteReturnEntityAsync();
        return await MapToDto(result);
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    public async Task<UserDto> UpdateAsync(UpdateUserDto dto, long? operatorUserId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == dto.Id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            throw Oops.Bah("用户不存在");
        }

        // 检查用户名是否已存在（排除当前用户）
        if (await IsUsernameExistsAsync(dto.Username, dto.Id))
        {
            throw Oops.Bah($"用户名 {dto.Username} 已存在");
        }

        entity.Username = dto.Username;
        entity.Role = dto.Role;
        entity.Status = dto.Status;
        entity.Remarks = dto.Remarks;
        entity.UpdatedTime = DateTime.Now;

        await DbContext.Instance.Updateable(entity).ExecuteCommandAsync();
        return await MapToDto(entity);
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    public async Task<bool> DeleteAsync(long id, long? operatorUserId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        // 不能删除超级管理员
        if (entity.Role == UserRole.SuperAdmin)
        {
            throw Oops.Bah("不能删除超级管理员");
        }

        entity.IsDeleted = true;
        entity.UpdatedTime = DateTime.Now;

        return await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    public async Task<UserDto?> GetByIdAsync(long id)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        return entity == null ? null : await MapToDto(entity);
    }

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    public async Task<UserDto?> GetByUsernameAsync(string username)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Username == username && !x.IsDeleted)
            .FirstAsync();

        return entity == null ? null : await MapToDto(entity);
    }

    /// <summary>
    /// 分页查询用户
    /// </summary>
    public async Task<(List<UserDto> Items, int TotalCount)> GetPagedListAsync(QueryUserDto dto)
    {
        var query = DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => !x.IsDeleted);

        // 添加查询条件
        if (!string.IsNullOrEmpty(dto.Username))
        {
            query = query.Where(x => x.Username.Contains(dto.Username));
        }



        if (dto.Role.HasValue)
        {
            query = query.Where(x => x.Role == dto.Role.Value);
        }

        if (dto.Status.HasValue)
        {
            query = query.Where(x => x.Status == dto.Status.Value);
        }

        if (dto.CreatedStartTime.HasValue)
        {
            query = query.Where(x => x.CreatedTime >= dto.CreatedStartTime.Value);
        }

        if (dto.CreatedEndTime.HasValue)
        {
            query = query.Where(x => x.CreatedTime <= dto.CreatedEndTime.Value);
        }

        // 获取总数
        var totalCount = await query.CountAsync();

        // 分页查询
        var entities = await query
            .OrderByDescending(x => x.CreatedTime)
            .Skip((dto.PageIndex - 1) * dto.PageSize)
            .Take(dto.PageSize)
            .ToListAsync();

        var items = new List<UserDto>();
        foreach (var entity in entities)
        {
            items.Add(await MapToDto(entity));
        }

        return (items, totalCount);
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    public async Task<bool> ChangePasswordAsync(ChangePasswordDto dto)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == dto.UserId && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            throw Oops.Bah("用户不存在");
        }

        // 验证原密码
        if (!_encryptionService.VerifyPassword(dto.OldPassword, entity.PasswordHash, entity.Salt))
        {
            throw Oops.Bah("原密码错误");
        }

        // 生成新的盐值和密码哈希
        var newSalt = _encryptionService.GenerateSalt();
        var newPasswordHash = _encryptionService.GeneratePasswordHash(dto.NewPassword, newSalt);

        entity.PasswordHash = newPasswordHash;
        entity.Salt = newSalt;
        entity.UpdatedTime = DateTime.Now;

        return await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    public async Task<bool> ResetPasswordAsync(long userId, string newPassword, long? operatorUserId = null)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == userId && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        // 生成新的盐值和密码哈希
        var newSalt = _encryptionService.GenerateSalt();
        var newPasswordHash = _encryptionService.GeneratePasswordHash(newPassword, newSalt);

        entity.PasswordHash = newPasswordHash;
        entity.Salt = newSalt;
        entity.UpdatedTime = DateTime.Now;

        return await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    public async Task<bool> IsUsernameExistsAsync(string username, long? excludeId = null)
    {
        var query = DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Username == username && !x.IsDeleted);

        if (excludeId.HasValue)
        {
            query = query.Where(x => x.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// 启用用户
    /// </summary>
    public async Task<bool> EnableAsync(long id, long? operatorUserId = null)
    {
        return await UpdateUserStatusAsync(id, UserStatus.Normal);
    }

    /// <summary>
    /// 禁用用户
    /// </summary>
    public async Task<bool> DisableAsync(long id, long? operatorUserId = null)
    {
        return await UpdateUserStatusAsync(id, UserStatus.Disabled);
    }

    /// <summary>
    /// 锁定用户
    /// </summary>
    public async Task<bool> LockAsync(long id, long? operatorUserId = null)
    {
        return await UpdateUserStatusAsync(id, UserStatus.Locked);
    }

    /// <summary>
    /// 解锁用户
    /// </summary>
    public async Task<bool> UnlockAsync(long id, long? operatorUserId = null)
    {
        return await UpdateUserStatusAsync(id, UserStatus.Normal);
    }

    /// <summary>
    /// 更新最后登录时间
    /// </summary>
    public async Task<bool> UpdateLastLoginTimeAsync(long userId)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == userId && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        entity.LastLoginTime = DateTime.Now;
        entity.LoginCount++;

        return await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 初始化默认管理员用户
    /// </summary>
    public async Task<bool> InitializeDefaultAdminAsync()
    {
        // 检查是否已存在超级管理员
        var existsAdmin = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Role == UserRole.SuperAdmin && !x.IsDeleted)
            .AnyAsync();

        if (existsAdmin)
        {
            return true;
        }

        // 创建默认超级管理员
        var salt = _encryptionService.GenerateSalt();
        var passwordHash = _encryptionService.GeneratePasswordHash("admin123", salt);

        var admin = new Core.Entities.User
        {
            Username = "admin",
            PasswordHash = passwordHash,
            Salt = salt,

            Role = UserRole.SuperAdmin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认超级管理员"
        };

        var result = await DbContext.Instance.Insertable(admin).ExecuteCommandAsync();
        return result > 0;
    }

    /// <summary>
    /// 更新用户状态
    /// </summary>
    private async Task<bool> UpdateUserStatusAsync(long id, UserStatus status)
    {
        var entity = await DbContext.Instance.Queryable<Core.Entities.User>()
            .Where(x => x.Id == id && !x.IsDeleted)
            .FirstAsync();

        if (entity == null)
        {
            return false;
        }

        // 不能禁用或锁定超级管理员
        if (entity.Role == UserRole.SuperAdmin && status != UserStatus.Normal)
        {
            throw Oops.Bah("不能禁用或锁定超级管理员");
        }

        entity.Status = status;
        entity.UpdatedTime = DateTime.Now;

        return await DbContext.Instance.Updateable(entity).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 映射到DTO
    /// </summary>
    private async Task<UserDto> MapToDto(Core.Entities.User entity)
    {
        return new UserDto
        {
            Id = entity.Id,
            Username = entity.Username,
            Role = entity.Role,
            RoleDescription = GetRoleDescription(entity.Role),
            Status = entity.Status,
            StatusDescription = GetStatusDescription(entity.Status),
            CreatedTime = entity.CreatedTime,
            UpdatedTime = entity.UpdatedTime,
            LastLoginTime = entity.LastLoginTime,
            LoginCount = entity.LoginCount,
            Remarks = entity.Remarks
        };
    }

    /// <summary>
    /// 获取角色描述
    /// </summary>
    private string GetRoleDescription(UserRole role)
    {
        return role switch
        {
            UserRole.User => "普通用户",
            UserRole.Admin => "管理员",
            UserRole.SuperAdmin => "超级管理员",
            _ => "未知角色"
        };
    }

    /// <summary>
    /// 获取状态描述
    /// </summary>
    private string GetStatusDescription(UserStatus status)
    {
        return status switch
        {
            UserStatus.Normal => "正常",
            UserStatus.Disabled => "已禁用",
            UserStatus.Locked => "已锁定",
            _ => "未知状态"
        };
    }

    /// <summary>
    /// 生成访问令牌（简化实现）
    /// </summary>
    private string GenerateAccessToken(Core.Entities.User user)
    {
        // 这里简化处理，实际项目中应该使用JWT
        var tokenData = $"{user.Id}:{user.Username}:{DateTime.Now.Ticks}";
        return Convert.ToBase64String(Encoding.UTF8.GetBytes(tokenData));
    }
}
