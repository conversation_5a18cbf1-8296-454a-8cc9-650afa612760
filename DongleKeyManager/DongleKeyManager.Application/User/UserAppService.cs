using DongleKeyManager.Application.DongleDevice;
using DongleKeyManager.Application.User.Dtos;
using DongleKeyManager.Application.User.Services;

namespace DongleKeyManager.Application.User;

/// <summary>
/// 用户管理API服务
/// </summary>
[ApiDescriptionSettings("User", Name = "User", Order = 2)]
public class UserAppService : IDynamicApiController, ITransient
{
    private readonly IUserService _userService;

    public UserAppService(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="dto">登录DTO</param>
    /// <returns>登录结果</returns>
    [HttpPost("login")]
    [ApiDescriptionSettings(Name = "Login")]
    [AllowAnonymous] // 登录接口允许匿名访问
    public async Task<LoginResultDto> LoginAsync([FromBody] LoginDto dto)
    {
        return await _userService.LoginAsync(dto);
    }

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="dto">创建用户DTO</param>
    /// <returns>用户信息</returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Create")]
    public async Task<UserDto> CreateAsync([FromBody] CreateUserDto dto)
    {
        return await _userService.CreateAsync(dto, GetCurrentUserId());
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="dto">更新用户DTO</param>
    /// <returns>用户信息</returns>
    [HttpPut]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task<UserDto> UpdateAsync([FromBody] UpdateUserDto dto)
    {
        return await _userService.UpdateAsync(dto, GetCurrentUserId());
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task<bool> DeleteAsync(long id)
    {
        return await _userService.DeleteAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户信息</returns>
    [HttpGet("{id}")]
    [ApiDescriptionSettings(Name = "GetById")]
    public async Task<UserDto?> GetByIdAsync(long id)
    {
        return await _userService.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>用户信息</returns>
    [HttpGet("username/{username}")]
    [ApiDescriptionSettings(Name = "GetByUsername")]
    public async Task<UserDto?> GetByUsernameAsync(string username)
    {
        return await _userService.GetByUsernameAsync(username);
    }

    /// <summary>
    /// 分页查询用户
    /// </summary>
    /// <param name="dto">查询条件</param>
    /// <returns>分页结果</returns>
    [HttpPost("query")]
    [ApiDescriptionSettings(Name = "GetPagedList")]
    public async Task<PagedResult<UserDto>> GetPagedListAsync([FromBody] QueryUserDto dto)
    {
        var (items, totalCount) = await _userService.GetPagedListAsync(dto);
        
        return new PagedResult<UserDto>
        {
            Items = items,
            TotalCount = totalCount,
            PageIndex = dto.PageIndex,
            PageSize = dto.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / dto.PageSize)
        };
    }

    /// <summary>
    /// 修改密码
    /// </summary>
    /// <param name="dto">修改密码DTO</param>
    /// <returns>修改结果</returns>
    [HttpPost("change-password")]
    [ApiDescriptionSettings(Name = "ChangePassword")]
    public async Task<bool> ChangePasswordAsync([FromBody] ChangePasswordDto dto)
    {
        return await _userService.ChangePasswordAsync(dto);
    }

    /// <summary>
    /// 重置密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="newPassword">新密码</param>
    /// <returns>重置结果</returns>
    [HttpPost("{userId}/reset-password")]
    [ApiDescriptionSettings(Name = "ResetPassword")]
    public async Task<bool> ResetPasswordAsync(long userId, [FromBody] string newPassword)
    {
        return await _userService.ResetPasswordAsync(userId, newPassword, GetCurrentUserId());
    }

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeId">排除的用户ID</param>
    /// <returns>是否存在</returns>
    [HttpGet("check-username")]
    [ApiDescriptionSettings(Name = "CheckUsername")]
    public async Task<bool> CheckUsernameAsync(string username, long? excludeId = null)
    {
        return await _userService.IsUsernameExistsAsync(username, excludeId);
    }

    /// <summary>
    /// 启用用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>启用结果</returns>
    [HttpPost("{id}/enable")]
    [ApiDescriptionSettings(Name = "Enable")]
    public async Task<bool> EnableAsync(long id)
    {
        return await _userService.EnableAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 禁用用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>禁用结果</returns>
    [HttpPost("{id}/disable")]
    [ApiDescriptionSettings(Name = "Disable")]
    public async Task<bool> DisableAsync(long id)
    {
        return await _userService.DisableAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 锁定用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>锁定结果</returns>
    [HttpPost("{id}/lock")]
    [ApiDescriptionSettings(Name = "Lock")]
    public async Task<bool> LockAsync(long id)
    {
        return await _userService.LockAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 解锁用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>解锁结果</returns>
    [HttpPost("{id}/unlock")]
    [ApiDescriptionSettings(Name = "Unlock")]
    public async Task<bool> UnlockAsync(long id)
    {
        return await _userService.UnlockAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 初始化默认管理员用户
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("init-admin")]
    [ApiDescriptionSettings(Name = "InitializeDefaultAdmin")]
    [AllowAnonymous] // 初始化接口允许匿名访问
    public async Task<bool> InitializeDefaultAdminAsync()
    {
        return await _userService.InitializeDefaultAdminAsync();
    }

    /// <summary>
    /// 获取当前用户ID（简化实现）
    /// </summary>
    /// <returns>用户ID</returns>
    private long? GetCurrentUserId()
    {
        // 这里简化处理，实际项目中应该从JWT令牌或会话中获取
        // 可以通过HttpContext.User.Claims获取用户信息
        return null;
    }
}
