using DongleKeyManager.Application.DongleDevice.Dtos;
using DongleKeyManager.Core.Entities;

namespace DongleKeyManager.Application.DongleDevice.Services;

/// <summary>
/// 设备管理服务接口
/// </summary>
public interface IDongleDeviceService
{
    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="dto">创建设备DTO</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>设备信息</returns>
    Task<DongleDeviceDto> CreateAsync(CreateDongleDeviceDto dto, long? userId = null);

    /// <summary>
    /// 更新设备
    /// </summary>
    /// <param name="dto">更新设备DTO</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>设备信息</returns>
    Task<DongleDeviceDto> UpdateAsync(UpdateDongleDeviceDto dto, long? userId = null);

    /// <summary>
    /// 删除设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteAsync(long id, long? userId = null);

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备信息</returns>
    Task<DongleDeviceDto?> GetByIdAsync(long id);

    /// <summary>
    /// 根据序列号获取设备
    /// </summary>
    /// <param name="serialNumber">序列号</param>
    /// <returns>设备信息</returns>
    Task<DongleDeviceDto?> GetBySerialNumberAsync(string serialNumber);

    /// <summary>
    /// 根据硬件唯一标识号获取设备
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <returns>设备信息</returns>
    Task<DongleDeviceDto?> GetByHardwareUniqueIdAsync(string hardwareUniqueId);

    /// <summary>
    /// 分页查询设备
    /// </summary>
    /// <param name="dto">查询条件</param>
    /// <returns>分页结果</returns>
    Task<(List<DongleDeviceDto> Items, int TotalCount)> GetPagedListAsync(QueryDongleDeviceDto dto);

    /// <summary>
    /// 激活设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>激活结果</returns>
    Task<bool> ActivateAsync(long id, long? userId = null);

    /// <summary>
    /// 禁用设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>禁用结果</returns>
    Task<bool> DisableAsync(long id, long? userId = null);

    /// <summary>
    /// 验证设备
    /// </summary>
    /// <param name="dto">验证DTO</param>
    /// <param name="userId">操作用户ID</param>
    /// <returns>验证结果</returns>
    Task<DeviceVerificationResultDto> VerifyAsync(VerifyDongleDeviceDto dto, long? userId = null);

    /// <summary>
    /// 检查序列号是否存在
    /// </summary>
    /// <param name="serialNumber">序列号</param>
    /// <param name="excludeId">排除的设备ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsSerialNumberExistsAsync(string serialNumber, long? excludeId = null);

    /// <summary>
    /// 检查硬件唯一标识号是否存在
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <param name="excludeId">排除的设备ID</param>
    /// <returns>是否存在</returns>
    Task<bool> IsHardwareUniqueIdExistsAsync(string hardwareUniqueId, long? excludeId = null);

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    Task<DeviceStatisticsDto> GetStatisticsAsync();
}

/// <summary>
/// 设备统计信息DTO
/// </summary>
public class DeviceStatisticsDto
{
    /// <summary>
    /// 总设备数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 已激活设备数
    /// </summary>
    public int ActiveCount { get; set; }

    /// <summary>
    /// 未激活设备数
    /// </summary>
    public int InactiveCount { get; set; }

    /// <summary>
    /// 已禁用设备数
    /// </summary>
    public int DisabledCount { get; set; }

    /// <summary>
    /// 已过期设备数
    /// </summary>
    public int ExpiredCount { get; set; }

    /// <summary>
    /// 今日新增设备数
    /// </summary>
    public int TodayAddedCount { get; set; }

    /// <summary>
    /// 本月新增设备数
    /// </summary>
    public int MonthAddedCount { get; set; }
}
