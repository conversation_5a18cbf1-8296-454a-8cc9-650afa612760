using DongleKeyManager.Application.DongleDevice.Dtos;
using DongleKeyManager.Application.DongleDevice.Services;

namespace DongleKeyManager.Application.DongleDevice;

/// <summary>
/// 设备管理API服务
/// </summary>
[ApiDescriptionSettings("DongleDevice", Name = "DongleDevice", Order = 1)]
public class DongleDeviceAppService : IDynamicApiController, ITransient
{
    private readonly IDongleDeviceService _dongleDeviceService;

    public DongleDeviceAppService(IDongleDeviceService dongleDeviceService)
    {
        _dongleDeviceService = dongleDeviceService;
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="dto">创建设备DTO</param>
    /// <returns>设备信息</returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Create")]
    public async Task<DongleDeviceDto> CreateAsync([FromBody] CreateDongleDeviceDto dto)
    {
        return await _dongleDeviceService.CreateAsync(dto, GetCurrentUserId());
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    /// <param name="dto">更新设备DTO</param>
    /// <returns>设备信息</returns>
    [HttpPut]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task<DongleDeviceDto> UpdateAsync([FromBody] UpdateDongleDeviceDto dto)
    {
        return await _dongleDeviceService.UpdateAsync(dto, GetCurrentUserId());
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task<bool> DeleteAsync(long id)
    {
        return await _dongleDeviceService.DeleteAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备信息</returns>
    [HttpGet("{id}")]
    [ApiDescriptionSettings(Name = "GetById")]
    public async Task<DongleDeviceDto?> GetByIdAsync(long id)
    {
        return await _dongleDeviceService.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据序列号获取设备
    /// </summary>
    /// <param name="serialNumber">序列号</param>
    /// <returns>设备信息</returns>
    [HttpGet("serial/{serialNumber}")]
    [ApiDescriptionSettings(Name = "GetBySerialNumber")]
    public async Task<DongleDeviceDto?> GetBySerialNumberAsync(string serialNumber)
    {
        return await _dongleDeviceService.GetBySerialNumberAsync(serialNumber);
    }

    /// <summary>
    /// 根据硬件唯一标识号获取设备
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <returns>设备信息</returns>
    [HttpGet("hardware/{hardwareUniqueId}")]
    [ApiDescriptionSettings(Name = "GetByHardwareUniqueId")]
    public async Task<DongleDeviceDto?> GetByHardwareUniqueIdAsync(string hardwareUniqueId)
    {
        return await _dongleDeviceService.GetByHardwareUniqueIdAsync(hardwareUniqueId);
    }

    /// <summary>
    /// 分页查询设备
    /// </summary>
    /// <param name="dto">查询条件</param>
    /// <returns>分页结果</returns>
    [HttpPost("query")]
    [ApiDescriptionSettings(Name = "GetPagedList")]
    public async Task<PagedResult<DongleDeviceDto>> GetPagedListAsync([FromBody] QueryDongleDeviceDto dto)
    {
        var (items, totalCount) = await _dongleDeviceService.GetPagedListAsync(dto);
        
        return new PagedResult<DongleDeviceDto>
        {
            Items = items,
            TotalCount = totalCount,
            PageIndex = dto.PageIndex,
            PageSize = dto.PageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / dto.PageSize)
        };
    }

    /// <summary>
    /// 激活设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>激活结果</returns>
    [HttpPost("{id}/activate")]
    [ApiDescriptionSettings(Name = "Activate")]
    public async Task<bool> ActivateAsync(long id)
    {
        return await _dongleDeviceService.ActivateAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 禁用设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>禁用结果</returns>
    [HttpPost("{id}/disable")]
    [ApiDescriptionSettings(Name = "Disable")]
    public async Task<bool> DisableAsync(long id)
    {
        return await _dongleDeviceService.DisableAsync(id, GetCurrentUserId());
    }

    /// <summary>
    /// 验证设备
    /// </summary>
    /// <param name="dto">验证DTO</param>
    /// <returns>验证结果</returns>
    [HttpPost("verify")]
    [ApiDescriptionSettings(Name = "Verify")]
    [AllowAnonymous] // 设备验证接口允许匿名访问
    public async Task<DeviceVerificationResultDto> VerifyAsync([FromBody] VerifyDongleDeviceDto dto)
    {
        return await _dongleDeviceService.VerifyAsync(dto, GetCurrentUserId());
    }

    /// <summary>
    /// 检查序列号是否存在
    /// </summary>
    /// <param name="serialNumber">序列号</param>
    /// <param name="excludeId">排除的设备ID</param>
    /// <returns>是否存在</returns>
    [HttpGet("check-serial")]
    [ApiDescriptionSettings(Name = "CheckSerialNumber")]
    public async Task<bool> CheckSerialNumberAsync(string serialNumber, long? excludeId = null)
    {
        return await _dongleDeviceService.IsSerialNumberExistsAsync(serialNumber, excludeId);
    }

    /// <summary>
    /// 检查硬件唯一标识号是否存在
    /// </summary>
    /// <param name="hardwareUniqueId">硬件唯一标识号</param>
    /// <param name="excludeId">排除的设备ID</param>
    /// <returns>是否存在</returns>
    [HttpGet("check-hardware")]
    [ApiDescriptionSettings(Name = "CheckHardwareUniqueId")]
    public async Task<bool> CheckHardwareUniqueIdAsync(string hardwareUniqueId, long? excludeId = null)
    {
        return await _dongleDeviceService.IsHardwareUniqueIdExistsAsync(hardwareUniqueId, excludeId);
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    [ApiDescriptionSettings(Name = "GetStatistics")]
    public async Task<DeviceStatisticsDto> GetStatisticsAsync()
    {
        return await _dongleDeviceService.GetStatisticsAsync();
    }

    /// <summary>
    /// 获取当前用户ID（简化实现）
    /// </summary>
    /// <returns>用户ID</returns>
    private long? GetCurrentUserId()
    {
        // 这里简化处理，实际项目中应该从JWT令牌或会话中获取
        // 可以通过HttpContext.User.Claims获取用户信息
        return null;
    }
}

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据列表
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages;
}
