using DongleKeyManager.Core.Entities;
using DongleKeyManager.Core.SqlSugar;
using System.Security.Cryptography;
using System.Text;

namespace DongleKeyManager.Core.SeedData;

/// <summary>
/// 设备种子数据
/// </summary>
public class DongleDeviceSeedData : ISqlSugarEntitySeedData<DongleDevice>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<DongleDevice> HasData()
    {
        var baseTime = DateTime.Now.AddDays(-30); // 30天前创建

        // 演示设备1 - 已激活
        var device1HardwareId = "HW-DEMO-001-ABCD1234";
        yield return new DongleDevice
        {
            Id = 2000001,
            SerialNumber = "SN-DEMO-001",
            HardwareUniqueId = device1HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device1HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备001",
            Description = "用于系统演示的看门狗设备",
            CreatedTime = baseTime,
            ActivatedTime = baseTime.AddDays(1),
            ExpiryTime = DateTime.Now.AddYears(1),
            LastUsedTime = DateTime.Now.AddHours(-2),
            UsageCount = 156,
            CreatedUserId = 1000002, // admin创建
            Remarks = "演示用设备，已激活状态"
        };

        // 演示设备2 - 未激活
        var device2HardwareId = "HW-DEMO-002-EFGH5678";
        yield return new DongleDevice
        {
            Id = 2000002,
            SerialNumber = "SN-DEMO-002",
            HardwareUniqueId = device2HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device2HardwareId),
            Status = DeviceStatus.Inactive,
            DeviceName = "演示设备002",
            Description = "待激活的看门狗设备",
            CreatedTime = baseTime.AddDays(5),
            ExpiryTime = DateTime.Now.AddYears(1),
            UsageCount = 0,
            CreatedUserId = 1000002, // admin创建
            Remarks = "演示用设备，未激活状态"
        };

        // 演示设备3 - 已禁用
        var device3HardwareId = "HW-DEMO-003-IJKL9012";
        yield return new DongleDevice
        {
            Id = 2000003,
            SerialNumber = "SN-DEMO-003",
            HardwareUniqueId = device3HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device3HardwareId),
            Status = DeviceStatus.Disabled,
            DeviceName = "演示设备003",
            Description = "已禁用的看门狗设备",
            CreatedTime = baseTime.AddDays(10),
            ActivatedTime = baseTime.AddDays(11),
            ExpiryTime = DateTime.Now.AddYears(1),
            LastUsedTime = DateTime.Now.AddDays(-5),
            UsageCount = 89,
            CreatedUserId = 1000002, // admin创建
            UpdatedUserId = 1000001, // superadmin禁用
            UpdatedTime = DateTime.Now.AddDays(-3),
            Remarks = "演示用设备，已禁用状态"
        };

        // 演示设备4 - 即将过期
        var device4HardwareId = "HW-DEMO-004-MNOP3456";
        yield return new DongleDevice
        {
            Id = 2000004,
            SerialNumber = "SN-DEMO-004",
            HardwareUniqueId = device4HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device4HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备004",
            Description = "即将过期的看门狗设备",
            CreatedTime = baseTime.AddDays(15),
            ActivatedTime = baseTime.AddDays(16),
            ExpiryTime = DateTime.Now.AddDays(7), // 7天后过期
            LastUsedTime = DateTime.Now.AddMinutes(-30),
            UsageCount = 234,
            CreatedUserId = 1000003, // testuser创建
            Remarks = "演示用设备，即将过期"
        };

        // 演示设备5 - 高使用频率
        var device5HardwareId = "HW-DEMO-005-QRST7890";
        yield return new DongleDevice
        {
            Id = 2000005,
            SerialNumber = "SN-DEMO-005",
            HardwareUniqueId = device5HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device5HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备005",
            Description = "高频使用的看门狗设备",
            CreatedTime = baseTime.AddDays(20),
            ActivatedTime = baseTime.AddDays(21),
            ExpiryTime = DateTime.Now.AddYears(2),
            LastUsedTime = DateTime.Now.AddMinutes(-5),
            UsageCount = 1567,
            CreatedUserId = 1000004, // demo创建
            Remarks = "演示用设备，高频使用"
        };

        // 演示设备6 - 长期未使用
        var device6HardwareId = "HW-DEMO-006-UVWX1234";
        yield return new DongleDevice
        {
            Id = 2000006,
            SerialNumber = "SN-DEMO-006",
            HardwareUniqueId = device6HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device6HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备006",
            Description = "长期未使用的看门狗设备",
            CreatedTime = baseTime.AddDays(25),
            ActivatedTime = baseTime.AddDays(26),
            ExpiryTime = DateTime.Now.AddYears(1),
            LastUsedTime = DateTime.Now.AddDays(-15), // 15天前最后使用
            UsageCount = 23,
            CreatedUserId = 1000003, // testuser创建
            Remarks = "演示用设备，长期未使用"
        };

        // 演示设备7 - 新创建设备
        var device7HardwareId = "HW-DEMO-007-YZAB5678";
        yield return new DongleDevice
        {
            Id = 2000007,
            SerialNumber = "SN-DEMO-007",
            HardwareUniqueId = device7HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device7HardwareId),
            Status = DeviceStatus.Inactive,
            DeviceName = "演示设备007",
            Description = "新创建的看门狗设备",
            CreatedTime = DateTime.Now.AddDays(-2), // 2天前创建
            ExpiryTime = DateTime.Now.AddYears(1),
            UsageCount = 0,
            CreatedUserId = 1000004, // demo创建
            Remarks = "演示用设备，新创建"
        };

        // 演示设备8 - 测试设备
        var device8HardwareId = "HW-TEST-008-CDEF9012";
        yield return new DongleDevice
        {
            Id = 2000008,
            SerialNumber = "SN-TEST-008",
            HardwareUniqueId = device8HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device8HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "测试设备008",
            Description = "用于功能测试的看门狗设备",
            CreatedTime = baseTime.AddDays(12),
            ActivatedTime = baseTime.AddDays(13),
            ExpiryTime = DateTime.Now.AddMonths(6),
            LastUsedTime = DateTime.Now.AddHours(-1),
            UsageCount = 445,
            CreatedUserId = 1000003, // testuser创建
            Remarks = "测试专用设备"
        };
    }

    /// <summary>
    /// 基于硬件唯一标识号生成加密字符串
    /// </summary>
    private static string GenerateDeviceEncryptedString(string hardwareUniqueId)
    {
        var saltedInput = hardwareUniqueId + "DongleKeyManager2024";
        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(saltedInput);
        var hashBytes = md5.ComputeHash(inputBytes);

        var sb = new StringBuilder();
        foreach (var b in hashBytes)
        {
            sb.Append(b.ToString("x2"));
        }

        return sb.ToString();
    }
}
