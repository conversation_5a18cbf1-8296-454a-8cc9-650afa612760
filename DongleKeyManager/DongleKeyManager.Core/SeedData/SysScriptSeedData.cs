namespace Feng.IotGateway.Core.SeedData;

public class SysScriptSeedData : ISqlSugarEntitySeedData<SysScript>
{
    public IEnumerable<SysScript> HasData()
    {
        return new[]
        {
            #region 设备操作

            new SysScript
            {
                Id = 307305999867973, Name = "属性-最新值", Content = "device.Get('${this.DeviceName}.标识');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.Get()\n// 参数: ${this.DeviceName} 为变量默认替换成当前设备名称,也可以直接使用设备名称, product  标识符\n// 返回类型:Object\n// 描述:该方法获取设备的属性值,返回object,如果不存在返回null\nvar product = device.Get('${this.DeviceName}.product');\nreturn product;\n\n// 示例返回结果\n125 or \"Px309382\" or true or null"
            },
            new SysScript
            {
                Id = 307305999867970, Name = "属性-返回对象", Content = "device.GetData('${this.DeviceName}.标识');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.GetData()\n// 参数:${this.DeviceName} 为变量默认替换成当前设备名称,也可以直接使用设备名称, product  设备标识\n// 返回类型:Object\n// 描述:该方法获取设备的某个属性的采集内容,返回对象\nvar product = device.GetData('${this.DeviceName}.product');\nreturn product;\n\n// 示例返回结果\n{\n  \"Value\": 125,\n  \"CookieValue\": 125,\n  \"CookieTime\": 1680589471312,\n  \"Message\": null,\n  \"ErrorCode\": 0,\n  \"VariableStatus\": 1,\n  \"DataType\": 4,\n  \"Id\": 400671793541317,\n  \"Identifier\": \"product\",\n  \"ReadTime\": 1680589472355\n}"
            },
            new SysScript
            {
                Id = 307305999867974, Name = "属性-Cookie值", Content = "device.Cookie('${this.DeviceName}.标识');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.Cookie()\n// 参数: ${this.DeviceName} 为变量默认替换成当前设备名称,也可以直接使用设备名称, product  标识符\n// 返回类型:Object\n// 描述:该方法获取设备的属性的上一次的值,返回object,如果不存在返回null\nvar product = device.Cookie('${this.DeviceName}.product');\nreturn product;\n\n// 示例返回结果\n125 or \"Px309382\" or true or null"
            },
            new SysScript
            {
                Id = 307305999867975, Name = "设备-连接状态", Content = "device.Status('${this.DeviceName}');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.Status()\n// 无参调用返回全部设备的状态\n// 参数一: '${this.DeviceName}'  （变量）代表当前设备名称,支持指定其他设备名称,例如'NS99' 非必填\n// 参数二: '码垛机'  设备别名,设备名称相同时可以根据别名指定设备, 非必填\n// 参数三: 30  持续时间,单位(秒):默认0,设置非0下 用于判断该状态是否持续了指定秒,例如 持续超过30秒返回true，否则就算是连接状态也返回false\n// 返回类型:bool\n// 描述:该方法获取设备的连接状态,返回Bool\nvar status = device.Status();\nreturn status;\n\n"
            },
            new SysScript
            {
                Id = 307305999867976, Name = "设备标签-返回对象", Content = "device.GetDeviceVariableByTag('${this.DeviceName}','属性标签');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.GetDeviceVariableByTag()\n// 参数: '报警'  标签名称\n// 返回类型:List<DeviceVariable>\n// 描述:该方法获取设备带有该标签名称的属性标识符,返回List<String>\nvar tagList = device.GetDeviceVariableByTag('${this.DeviceName}', '报警');\nreturn tagList;\n\n// 示例返回结果\n{\r\n\t\"Value\": 0,\r\n\t\"CookieValue\": 0,\r\n\t\"CookieTime\": 1682326572666,\r\n\t\"Message\": null,\r\n\t\"ErrorCode\": 0,\r\n\t\"VariableStatus\": 1,\r\n\t\"DataType\": 4,\r\n\t\"Id\": 410241210466501,\r\n\t\"Identifier\": \"wqwj\",\r\n\t\"ReadTime\": 1682326574072\r\n}"
            },
            new SysScript
            {
                Id = 307305999867977, Name = "设备标签-返回标识和值", Content = "device.GetIdentifierAndValueByTag('${this.DeviceName}','属性标签');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.GetIdentifierAndValueByTag()\n// 参数: '报警'  标签名称\n// 返回类型:dic<string,objce>\n// 描述:该方法获取设备带有该标签名称的属性标识和值,返回dic<string,objce>\nvar tagList = device.GetIdentifierAndValueByTag('${this.DeviceName}', '报警');\nreturn tagList;\n\n// 示例返回结果\n{\"product\":144}"
            },
            new SysScript
            {
                Id = 307305999867978, Name = "设备标签-返回对象和值", Content = "device.GetDeviceVariableAndValueByTag('${this.DeviceName}','属性标签');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.GetDeviceVariableAndValueByTag()\r\n// 参数: '报警'  标签名称\r\n// 返回类型:dic<DeviceVariable,objce>\r\n// 描述:根据设备标签-返回属性和值,返回dic<DeviceVariable,objce>\r\nvar tagList = device.GetDeviceVariableAndValueByTag('${this.DeviceName}', '报警');\r\nreturn tagList;\r\n\r\n// 示例返回结果\r\n{\"M1701\":true}"
            },
            new SysScript
            {
                Id = 307305999867979, Name = "设备-（手动写值）写入数据", Content = "device.WriteDataToStatic('${this.DeviceName}','属性集合');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.WriteDataToStatic()\n// 参数一: '${this.DeviceName}'  设备名称\n// 参数二: 'propertyList' 字典对象\n// 参数三: 'true' 非必填，默认false，是否支持表达式解析字符串\n// 返回类型:List<object>\n// 描述:向设备地址写入数据-该方法仅支持改变属性类型为:【手动写值】属性,返回List<object>\n\nvar propertyList = {\n \"product\" :10,\n \"status\": 1\n}\nvar tagList = device.WriteDataToStatic('${this.DeviceName}', propertyList);\nreturn tagList;\n\n// 示例返回结果\n[{\"product\":true},{\"status\":false}]"
            },
            new SysScript
            {
                Id = 307305999867980, Name = "设备-PLC地址写入数据", Content = "device.WriteDataToDevice('${this.DeviceName}','属性集合');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.WriteDataToDevice()\n// 参数一: '${this.DeviceName}'  设备名称\n// 参数二: 'propertyList' 字典对象\n// 参数三: 'true' 非必填，默认false，是否支持表达式解析字符串\n// 返回类型:List<object>\n// 描述:向设备地址写入数据-该方法仅支持改变属性类型为:【直接读取】属性,返回List<object>\n\nvar propertyList = {\n \"product\" :10,\n \"status\": 1\n}\nvar tagList = device.WriteDataToDevice('${this.DeviceName}', propertyList);\nreturn tagList;\n\n// 示例返回结果\n[{\"product\":true},{\"status\":false}]"
            },
            new SysScript
            {
                Id = 307305999867981, Name = "设备-通用写入数据", Content = "device.SetVariable('${this.DeviceName}','属性集合');", ScriptType = SysScriptTypeEnum.Device,
                Describe =
                    "// 方法:device.SetVariable()\n// 参数一: '${this.DeviceName}'  设备名称\n// 参数二: 'propertyList' 字典对象\n// 参数三: 'true' 非必填，默认false，是否支持表达式解析字符串\n// 返回类型:List<object>\n// 描述:向设备写入数据-该方法仅支持改变属性类型为:【手动写值,直接读取】属性,返回List<object>\n\nvar propertyList = {\n \"product\" :10,\n \"status\": 1\n}\nvar tagList = device.SetVariable('${this.DeviceName}', propertyList);\nreturn tagList;\n\n// 示例返回结果\n[{\"product\":true},{\"status\":false}]"
            },

            #endregion 设备操作

            #region 共享数据操作

            new SysScript
            {
                Id = 307305999867983, Name = "保存临时变量(持久化)", Content = "share.Save('变量名', '变量值');", ScriptType = SysScriptTypeEnum.OpenData,
                Describe =
                    "// 方法:share.Save()\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: false 非必填,标记是否触发任务，默认false\r\n// 返回类型:bool\r\n// 描述:该方法持久化保存标签和值,全局唯一,相同标识会覆盖,返回bool\r\n\r\n// 重载方法一:\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: 60 过期时间(秒) 60秒后自动删除该变量\r\n// 参数四: false 非必填,标记是否触发任务，默认false\r\n// share.Save('status', 1,60);\r\n\r\n// 重载方法二:\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: '23:00:00' 过期时间，指定'23:00:00'自动删除该变量，如超过当日则推迟到下一个时间点\r\n// 参数四: false 非必填,标记是否触发任务，默认false\r\n// share.Save('status', 1,'23:00:00'); // '23:00:00' 当日'23:00:00'后自动删除该变量\r\n\r\n// 示例一\r\nvar save = share.Save('status', 1);\r\nreturn save;"
            },
            new SysScript
            {
                Id = 307305999867984, Name = "保存临时变量", Content = "share.Set('变量名', '变量值');", ScriptType = SysScriptTypeEnum.OpenData,
                Describe =
                    "// 方法:share.Set()\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: false 非必填,标记是否触发任务，默认false\r\n// 返回类型:bool\r\n// 描述:该方法临时保存标签和值,全局唯一,相同标识会覆盖,服务重启后丢失,返回bool\r\n\r\n// 重载方法一:\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: 60 过期时间(秒) 60秒后自动删除该变量\r\n// 参数四: false 非必填,标记是否触发任务，默认false\r\n// share.Set('status', 1,60);\r\n\r\n// 重载方法二:\r\n// 参数一: 'status'  标签名称, \r\n// 参数二: 1 值[object类型]\r\n// 参数三: '23:00:00' 过期时间，指定'23:00:00'自动删除该变量，如超过当日则推迟到下一个时间点\r\n// 参数四: false 非必填,标记是否触发任务，默认false\r\n// share.Set('status', 1,'23:00:00'); // '23:00:00' 当日'23:00:00'后自动删除该变量\r\n\r\n// 示例一\r\nvar save = share.Set('status', 1);\r\nreturn save;"
            },
            new SysScript
            {
                Id = 307305999867985, Name = "获取临时变量", Content = "share.Get('变量名');", ScriptType = SysScriptTypeEnum.OpenData,
                Describe = "// 方法:share.Get()\n// 参数: 'status'  标签名称\n// 返回类型:object\n// 描述:该方法获取临时标签和值,返回object\nvar value = share.Get('status');\nreturn value;"
            },
            new SysScript
            {
                Id = 307305999867986, Name = "删除临时变量", Content = "share.Remove('变量名');", ScriptType = SysScriptTypeEnum.OpenData,
                Describe = "// 方法:share.Remove()\n// 参数: 'status'  标签名称\n// 返回类型:bool\n// 描述:该方法删除临时标签和值,返回bool\nvar save = share.Remove('status');\nreturn save;"
            },

            #endregion 共享数据操作

            #region 时间操作

            new SysScript
            {
                Id = 307305999867987, Name = "当前时间", Content = "dateTime.Now();", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.Now()\n// 参数: 'cst' 时区,非必填,默认返回当前时区时间,设置cst强制返回北京时间\n// 返回类型:datetime\n// 描述:该方法获取网关当前时间,如果网关时区为UTC则返回UTC/CST格式时间,返回datetime\nvar time = dateTime.Now();\nreturn time;\n\n// 示例返回结果\n2023-04-04 07:54:14"
            },
            new SysScript
            {
                Id = 307305999867994, Name = "格式化时间", Content = "dateTime.Format('时间','yyyy-MM-dd HH:mm:ss');", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.Format()\n// 参数一: 'time'  时间支持DateTime和字符串格式\n//参数二: 'yyyy-MM-dd HH:mm:ss' 非必填,默认转换格式,支持自定义如:'yyyy-MM-dd' or 'yyyy-MM-dd HH:mm' \n// 返回类型:string\n// 描述:该方法格式化时间,返回字符串\nvar time = '2023-04-04 08:31:03';\nvar status = dateTime.Format(time,'yyyy-MM-dd HH:mm:ss');\nreturn status;\n\n// 示例返回结果\n2023-04-04 08:31"
            },
            new SysScript
            {
                Id = 307305999867988, Name = "时间差", Content = "dateTime.DiffDate('开始时间','结束时间','偏移量');", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.DiffDate()\n// 参数一: 'startTime'  开始时间 \n//参数二: 'endTime' 结束时间\n//参数三: '偏移量' 非必填 单位(小时)\n// 返回类型:double\n// 描述:该方法返回开始时间到结束时间的毫秒级时间差值,可通过设置偏移量增加结束时间,返回double\nvar startTime = dateTime.Now();\nvar endTime =  dateTime.Now();\nvar time = dateTime.DiffDate(startTime,endTime,1);\nreturn time;\n\n// 示例返回结果\n3600000"
            },
            new SysScript
            {
                Id = 307305999867989, Name = "增减时间", Content = "dateTime.AddDate(时间,单位,值);", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.AddDate()\n// 参数: nowTime  时间,'day' 单位(其他:year,month,day,hour,min,sec), 1 值\n// 返回类型:double\n// 描述:该方法返回开始时间到结束时间的毫秒级时间差值,可通过设置偏移量增加结束时间,返回double\nvar nowTime = '2023-04-04 08:53:03';\nvar time = dateTime.AddDate(nowTime,'day',1);\nreturn time;\n\n// 示例返回结果\n2023-04-05 08:53:03"
            },
            new SysScript
            {
                Id = 307305999867901, Name = "毫秒级时间戳", Content = "dateTime.ToMilliseconds(时间);", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.ToMilliseconds()\n// 参数: nowTime  时间 非必填,不填则默认当前时间\n// 返回类型:long\n// 描述:该方法返回时间的毫秒级时间戳,返回long\nvar nowTime = '2023-04-04 08:53:03';\nvar time = dateTime.ToMilliseconds(nowTime);\nreturn time;\n\n// 示例返回结果\n1680598383000"
            },
            new SysScript
            {
                Id = 307305999867902, Name = "毫秒时间戳转北京时间", Content = "dateTime.ToLocalTime(时间戳);", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.ToLocalTime()\n// 参数: nowTime 毫秒级时间戳\n// 返回类型:string\n// 描述:该方法返回毫秒级时间戳转换的北京时间,返回string\nvar timeStamp = 1680598383000;\nvar time = dateTime.ToLocalTime(timeStamp);\nreturn time;\n\n// 示例返回结果\n2023-04-04 08:53:03"
            },
            new SysScript
            {
                Id = 307305999867903, Name = "秒级时间戳", Content = "dateTime.ToSeconds(时间);", ScriptType = SysScriptTypeEnum.DateTime,
                Describe =
                    "// 方法:dateTime.ToSeconds()\n// 参数: nowTime  时间 非必填,不填则默认当前时间\n// 返回类型:long\n// 描述:该方法返回时间的秒级时间戳,返回long\nvar nowTime = '2023-04-04 08:53:03';\nvar time = dateTime.ToSeconds(nowTime);\nreturn time;\n\n// 示例返回结果\n1680598383"
            },

            #endregion 时间操作

            #region 系统方法

            new SysScript
            {
                Id = 313606099075141, Name = "打印日志", Content = "log.Write('日志内容,保存日志');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:log.Write()\n// 参数一: 'strVal'  打印参数内容\n // 参数二: 'true'  非必填,是否保存日志\n // 返回类型:object\n// 描述:该方法用于打印日志,返回object\nvar strVal = 'hello word';\nlog.Write(strVal);\n\n// 示例返回结果\nhello word"
            },
            new SysScript
            {
                Id = 313606099075142, Name = "十进制转二进制", Content = "system.Binary('int值');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Binary()\n// 参数: intValue  int类型\n// 返回类型:string\n// 描述:该方法用于将十进制数字转换成二进制,返回string\nvar intValue = 10;\nvar value = system.Binary(intValue);\nreturn value;\n\n// 示例返回结果\n1010"
            },
            new SysScript
            {
                Id = 313606099075143, Name = "二进制转十进制", Content = "system.Decimal('字符串值');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Decimal()\n// 参数: strValue  string类型\n// 返回类型:int\n// 描述:该方法用于将二进制转换成十进制数字,返回int\nvar strValue = '1010';\nvar value = system.Decimal(strValue);\nreturn value;\n\n// 示例返回结果\n10"
            },
            new SysScript
            {
                Id = 313606099075144, Name = "Md5加密", Content = "system.Md5Encryption('字符串值',false,false);", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Md5Encryption()\r\n// 参数: strValue  string类型 加密字符串，\r\n// 参数: false 非必填 是否将返回结果转成大写,默认false, \r\n// 参数: false 非必填 是否返回16位字符串\r\n// 返回类型:string\r\n// 描述:该方法用于将字符串通过MD5的方式进行加密处理,返回string\r\nvar strValue = 'hell word';\r\nvar value = system.Md5Encryption(strValue,true,false);\r\nreturn value;\r\n\r\n// 示例返回结果\r\n// system.Md5Encryption(strValue,false,false);  返回结果如下\r\na166985348271b3368e596d8d0bb92fc\r\n// system.Md5Encryption(strValue,true,false);  返回结果如下\r\nA166985348271B3368E596D8D0BB92FC\r\n// system.Md5Encryption(strValue,true,true);  返回结果如下\r\n48271B3368E596D8"
            },
            // new SysScript {Id = 313606099075145, Name = "Md5解密", Content = "system.Md5EncryptionToUpper('字符串值');", ScriptType = SysScriptTypeEnum.SystemServices, Describe = ""},
            new SysScript
            {
                Id = 313606099075146, Name = "转换字节数组", Content = "system.ToBytes('int16','270');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.ToBytes()\n// 参数: 'int16'  数据类型:['bool','int16','uint16','uint32','int32','uint64','int64','double','float','utf8','utf32','ascii','unicode','unicode-big','gb2312','base64'],\n// 参数二: strValue 字符串类型数字\n// 返回类型:byte[]\n// 描述:该方法用于将字符数字转成字节数组,返回byte[]\nvar strValue = '270';\nvar value = system.ToBytes('int16', strValue);\nreturn value;\n\n// 示例返回结果\n[\n  14,\n  1\n]"
            },
            new SysScript
            {
                Id = 313606099075147, Name = "值转字符串", Content = "system.HexToString('int16','270');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.HexToString()\n// 参数: 'int16'  数据类型:['bool','int16','uint16','uint32','int32','uint64','int64','double','float','utf8','utf32','ascii','unicode','unicode-big','gb2312','base64'],\n// 参数二: strValue 字符串类型\n// 返回类型:string\n// 描述:该方法用于将值转换成字符串,返回string\nvar strValue = '270';\nvar value = system.HexToString('int16', strValue);\nreturn value;\n\n// 示例返回结果\n010E"
            },
            new SysScript
            {
                Id = 313606099075148, Name = "数组转字符串", Content = "system.BytesToString('int16','0E 01 00 00');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.BytesToString()\n// 参数: 'int16'  数据类型:['bool','int16','uint16','uint32','int32','uint64','int64','double','float','utf8','utf32','ascii','unicode','unicode-big','gb2312','base64'],\n// 参数二: strValue 字符串类型\n// 返回类型:string\n// 描述:该方法用于将数组转换成字符串,返回string\nvar strValue = '0E 01 00 00';\nvar value = system.BytesToString('int16', strValue);\nreturn value;\n\n// 示例返回结果\n270"
            },

            new SysScript
            {
                Id = 313606099075149, Name = "Hex格式字符串", Content = "system.HexStringToBytes('hex');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.HexStringToBytes()\r\n// 参数: '01 03 00 00 00 01 84 0A' 16进制字符串\r\n// 返回类型:byte[]\r\n// 描述:该方法将16进制的字符串转化成Byte数据，将检测每2个字符转化，也就是说，中间可以是任意字符,返回byte[]\r\nvar byte = system.HexStringToBytes('01 03 00 00 00 01 84 0A');;\r\nreturn byte;"
            },

            new SysScript
            {
                Id = 313606099075150, Name = "ASCII格式的字符串", Content = "system.AscStringToBytes('render');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.AscStringToBytes()\r\n// 参数: 'TEST' ASCII格式的字符串\r\n// 返回类型:byte[]\r\n// 描述:该方法将ASCII格式的字符串转为原始字节数组，如果遇到 \\00 这种表示原始字节的内容，则直接进行转换操作，遇到 \r 直接转换 0x0D, \n 直接转换 0x0A,返回byte[]\r\nvar byte = system.AscStringToBytes('TEST');;\r\nreturn byte;"
            },

            new SysScript
            {
                Id = 313606099075151, Name = "字节转16进制字符串", Content = "system.ByteToHexString(byte[],'array');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.ByteToHexString()\r\n// 参数: 'lw' 字节数组\r\n// 参数: 'array' 返回数据格式,非必填,默认string, 支持:【string,array】\r\n// 返回类型:byte[] /string\r\n// 描述:该方法将字节数据转化成16进制表示的字符串或者数组,返回byte[] /string\r\nvar lw=new Array(123,1,2,4,5,6,7,8,2,43);\r\nvar strValue = system.ByteToHexString(lw,'array');\r\nreturn strValue;"
            },

            new SysScript
            {
                Id = 313606099075152, Name = "休眠", Content = "system.Sleep(1000);", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Sleep()\n// 参数: 1000 单位毫秒\n// 返回类型:无\n// 描述:该方法用于 休眠当前线程\nsystem.Sleep(1000);"
            },
            new SysScript
            {
                Id = 313606099075153, Name = "Ping", Content = "system.Ping('127.0.0.1',1000);", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Ping()\n// 参数一: '127.0.0.1' Ip地址\n// 参数二: 1000 超时时间,非必填\n// 返回类型:bool\n// 描述:该方法用于 Ping网络是否通畅,返回bool值\nsystem.Ping('127.0.0.1',1000);"
            },
            new SysScript
            {
                Id = 313606099075154, Name = "Cmd命令", Content = "system.Command('reboot','/bin/bash');", ScriptType = SysScriptTypeEnum.System,
                Describe =
                    "// 方法:system.Command()\n// 参数一: 'reboot' 执行命令\n// 参数二: '/bin/bash' window环境必填参数,执行cmd命令文件完整路径\n// 返回类型:string\n// 描述:该方法用于 执行Cmd命令,返回string值\nsystem.Command('reboot','/bin/bash');"
            },

            #endregion

            #region 串口相关函数

            new SysScript
            {
                Id = 428622098411717, Name = "打开串口", Content = "serial.OpenCom('串口号',波特率,数据位,停止位,校验位);", ScriptType = SysScriptTypeEnum.Serial,
                Describe =
                    "// 方法:serial.OpenCom()\r\n// 参数: '/dev/ttyS1'  串口号,网关支持:【/dev/ttyS1,/dev/ttyS3,/dev/ttyS5,/dev/ttyS0】\r\n// 参数: '9600'  波特率\r\n// 参数: '8'  数据位\r\n// 参数: '1'  停止位,网关支持:【0:None，1:One,2:Two,3:OnePointFive】\r\n// 参数: '0'  校验位,网关支持:【0:None,1:Odd,2:Even,3:Mark,4:Space】\r\n// 返回类型:Bool\r\n// 描述:该方法打开串口,返回Bool\r\nvar isOpen = serial.OpenCom('/dev/ttyS1',9600,8,1,0);\r\nreturn isOpen;\r\n\r\n// 示例返回结果\r\ntrue or false"
            },

            new SysScript
            {
                Id = 428622098411718, Name = "发送消息", Content = "serial.SendCom('数据','hex',’byte‘,500);", ScriptType = SysScriptTypeEnum.Serial,
                Describe =
                    "// 方法:serial.SendCom()\r\n// 参数一: 'data'  发送内容（字节数组）\r\n// 参数二: 'byte'  非必填,默认返回byte,支持【binary,ascii,byte】\r\n// 参数三: '20'    非必填,默认20ms,等待时间,处理分包回复的场景\r\n// 参数四: 'none'  非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n// 返回类型:object\r\n// 描述:该方法串口发送消息,然后从串口接收一条数据,返回object\r\n\r\n// 重载方法\r\n// 参数一: 'data'  发送内容（字符串）\r\n// 参数二: 'ascii' 非必填,默认hex,字符编码,支持hex,ascii\r\n// 参数三: 'byte'  非必填,默认返回byte,支持【binary,ascii,byte】\r\n// 参数四: '20'    非必填,默认20ms,等待时间,处理分包回复的场景\r\n// 参数五: 'none'  非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n\r\n// 示例一\r\n// serial.SendCom('01 03 00 00 00 01 84 0A','ascii',20,'none');\r\n// 示例二\r\nvar isSend = serial.SendCom('12345678','hex','byte',20,'\\r');\r\nreturn isSend;"
            },

            // new SysScript
            // {
            //     Id = 428622098411719, Name = "接收数据", Content = "serial.ReceiveCom(1000,500,'byte');", ScriptType = SysScriptTypeEnum.Serial,
            //     Describe =
            //         "// 方法:serial.ReceiveCom()\r\n// 参数一: '1000'  休眠时间\r\n// 参数二: '500'  非必填,收包时间,默认1000(毫秒),1秒内收到的都算同一个包,注:休眠时间一定要大于收包时间,否则会出现收不到数据的问题 \r\n// 参数三: 'byte'  非必填,默认返回'byte',支持'string','ascii' \r\n// 返回类型:根据参数二配置决定返回,默认返回 byte[] 或 string\r\n// 描述:该方法串口接收数据,返回byte[] 或 string\r\nvar content = serial.ReceiveCom(1000,'byte');\r\nreturn content;"
            // },

            new SysScript
            {
                Id = 428622098411720, Name = "关闭串口", Content = "serial.CloseCom();", ScriptType = SysScriptTypeEnum.Serial,
                Describe =
                    "// 方法:serial.CloseCom()\r\n// 返回类型:bool\r\n// 描述:该方法关闭串口连接,返回bool\r\nvar isClose = serial.CloseCom();\r\nreturn isClose;\r\n\r\n//示例返回\r\ntrue or false"
            },

            #endregion 串口相关函数

            #region Http相关函数

            new SysScript
            {
                Id = 428622302314693, Name = "Get请求", Content = "http.Get('url','headers',query);", ScriptType = SysScriptTypeEnum.Http,
                Describe =
                    "// 方法:http.Get()\r\n// 参数: 'url'  API地址\r\n// 参数: 'headers'  请求头\r\n// 参数: 'query'  请求参数\r\n// 参数: 'timeout' 非必填 默认5秒 \r\n// 参数: 'contentType' 非必填 默认'application/json'\r\n// 返回类型:{\"success\":true,\"data\":\"string\"} \r\n// 描述:该方法用于Http的Get请求,返回object\r\n\r\n//示例 API地址\r\nvar url = 'http://127.0.0.1:9011/api/openapi/device/list';\r\n//示例 请求头\r\nvar headers = {\r\n  'Accept': 'application/json'\r\n}\r\n//示例 请求参数\r\nvar query = {\r\n  'appId': 423721361469637\r\n}\r\nvar data = http.Get(url, headers, query);\r\nreturn data\r\n\r\n//返回示例\r\n// {\r\n// \"success\": true,\r\n// \"data\": \"{\"statusCode\":200,\"data\":null,\"succeeded\":true,\"errors\":null,\"extras\":null,\"timestamp\":1686822592}\"\r\n// }"
            },

            new SysScript
            {
                Id = 428622302314694, Name = "Post请求", Content = "http.Post('url','headers',body);", ScriptType = SysScriptTypeEnum.Http,
                Describe =
                    "// 方法:http.Post()\r\n// 参数: 'url'  API地址\r\n// 参数: 'headers'  请求头\r\n// 参数: 'body'  请求参数\r\n// 参数: 'timeout' 非必填 默认5秒 \r\n// 参数: 'contentType' 非必填 默认'application/json'\r\n// 返回类型:{\"success\":true,\"data\":\"string\"} \r\n// 描述:该方法用于Http的Post请求,返回objce\r\n\r\n//示例 API地址\r\nvar url = 'http://127.0.0.1:9011/write';\r\n//示例 请求头\r\nvar headers = {\r\n  'Accept': 'application/json'\r\n}\r\n//示例 请求参数\r\nvar body = {\r\n  'data': {\r\n    'wd':10,\r\n    'sd':99.9\r\n  }\r\n}\r\nvar data = http.Post(url, headers, body);\r\nreturn data\r\n\r\n//返回示例\r\n// {\r\n// \"success\": true,\r\n// \"data\": \"{\"statusCode\":200,\"data\":null,\"succeeded\":true,\"errors\":null,\"extras\":null,\"timestamp\":1686822592}\"\r\n// }"
            },

            #endregion Http相关函数

            #region TCP连接相关函数

            new SysScript
            {
                Id = 437556098965701, Name = "TCP连接", Content = "tcp.Connect('Ip',端口);", ScriptType = SysScriptTypeEnum.Tcp,
                Describe =
                    "// 方法:tcp.Connect()\r\n// 参数: '127.0.0.1'  连接Ip地址\r\n// 参数: '9600'  端口号\r\n// 返回类型:Bool\r\n// 描述:该方法用于TCP的连接,返回Bool\r\nvar isConnect = tcp.Connect('127.0.0.1',9600);\r\nreturn isConnect;\r\n\r\n// 示例返回结果\r\ntrue or false"
            },

            new SysScript
            {
                Id = 437556098965702, Name = "发送消息", Content = "tcp.Send('数据');", ScriptType = SysScriptTypeEnum.Tcp,
                Describe =
                    "// 方法:tcp.Send()\r\n// 参数一: 'data'  数据（字节数组）\r\n// 参数二: 'byte' 非必填 返回格式默认byte,支持[ascii,byte,binary]\r\n// 参数三: '20'   非必填 等待时间,处理分包回复的场景,默认20\r\n// 参数四: 'none' 非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n// 返回类型:Object\r\n// 描述:该方法TCP发送消息,返回Object\r\n\r\n// 重载方法\r\n// 参数一: 'data' 字符串\r\n// 参数二: 'hex'  非必填  字符编码,默认hex,支持[hex,ascii]\r\n// 参数三: 'byte' 非必填  返回格式默认byte,支持[ascii,byte,binary]\r\n// 参数四: '20'   非必填  等待时间,处理分包回复的场景,默认20\r\n// 参数五: 'none' 非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n\r\n// 示例一\r\n// tcp.Send('01 03 00 00 00 01 84 0A','ascii',20,'none');\r\n// 示例二\r\nvar isSend = tcp.Send('12345678','hex','byte',20,'none');\r\nreturn isSend;"
            },

            // new SysScript
            // {
            //     Id = 437556098965703, Name = "接收数据", Content = "tcp.ReceiveCom(1000,'byte');", ScriptType = SysScriptTypeEnum.Tcp,
            //     Describe =
            //         "// 方法:tcp.ReceiveCom()\r\n// 参数一: '1000'  休眠时间\r\n// 参数二: 'byte'  非必填,默认返回'byte',支持'string','ascii' \r\n// 返回类型:根据参数二配置决定返回,默认返回 byte[] 或 string\r\n// 描述:该方法TCP接收数据,返回byte[] 或 string\r\nvar content = tcp.ReceiveCom(1000,'byte');\r\nreturn content;"
            // },

            new SysScript
            {
                Id = 437556098965704, Name = "关闭连接", Content = "tcp.Close();", ScriptType = SysScriptTypeEnum.Tcp,
                Describe =
                    "// 方法:tcp.Close()\r\n// 返回类型:bool\r\n// 描述:该方法关闭TCP连接,返回bool\r\nvar isClose = tcp.Close();\r\nreturn isClose;\r\n\r\n//示例返回\r\ntrue or false"
            },

            #endregion TCP连接相关函数

            #region UDP连接相关函数

            new SysScript
            {
                Id = 437556098965705, Name = "UDP连接", Content = "udp.Connect('Ip',端口);", ScriptType = SysScriptTypeEnum.Udp,
                Describe =
                    "// 方法:udp.Connect()\r\n// 参数: '127.0.0.1'  连接Ip地址\r\n// 参数: '9600'  端口号\r\n// 返回类型:Bool\r\n// 描述:该方法用于UDP的连接,返回Bool,注意:UDP是面向无连接的协议,不存在连接概念默认返回true\r\nvar isConnect = udp.Connect('127.0.0.1',9600);\r\nreturn isConnect;\r\n\r\n"
            },

            new SysScript
            {
                Id = 437556098965706, Name = "发送消息", Content = "udp.Send('数据');", ScriptType = SysScriptTypeEnum.Udp,
                Describe =
                    "// 方法:udp.Send()\r\n// 参数一: 'data'  数据（字节数组）\r\n// 参数二: 'byte' 非必填 返回格式默认byte,支持[ascii,byte,binary]\r\n// 参数三: 'none' 非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n// 返回类型:Object\r\n// 描述:该方法UDP发送消息,返回Object\r\n\r\n// 重载方法\r\n// 参数一: 'data' 字符串\r\n// 参数二: 'hex'  非必填  字符编码,默认hex,支持[hex,ascii]\r\n// 参数三: 'byte' 非必填  返回格式默认byte,支持[ascii,byte,binary]\r\n// 参数四: 'none' 非必填,默认none,追加字符,支持【\\r,\\n,\\r\\n,crc16】\r\n\r\n// 示例一\r\n// udp.Send('01 03 00 00 00 01 84 0A','ascii','none');\r\n// 示例二\r\nvar isSend = udp.Send('12345678','hex','byte','none');\r\nreturn isSend;"
            },

            #endregion UDP连接相关函数

            #region MQTT

            new SysScript
            {
                Id = 437556098965707, Name = "连接服务端", Content = "mqtt.Connect('127.0.0.1',1883);", ScriptType = SysScriptTypeEnum.Mqtt,
                Describe =
                    "// 方法:mqtt.Connect()\n// 参数一: '127.0.0.1'  MQTT服务器Ip地址\n// 参数二:  1883        MQTT服务器端口\n// 参数三: 'clientId'   MQTT连接唯一标识ClientId，不填默认随机生成, 非必填\n// 参数四: 'userName'   MQTT连接账号, 非必填\n// 参数五: 'password'   MQTT连接密码, 非必填\n// 返回类型:bool\n// 描述:连接MQTT服务器,返回Bool\nvar status = mqtt.Connect('127.0.0.1',1883,'clientId','userName','password');\nreturn status;"
            },

            new SysScript
            {
                Id = 437556098965708, Name = "发送主题消息", Content = "mqtt.Publish('127.0.0.1',1883);", ScriptType = SysScriptTypeEnum.Mqtt,
                Describe =
                    "// 方法:mqtt.Publish()\n// 参数一: '/pub/online'  消息主题\n// 参数二:  'xxxxx'       消息内容\n// 参数三:   1            MQTT消息质量，支持[0(最多传输一次),1(至少传输一次),2(恰好传输一次)],默认质量【1】 非必填\n// 参数四: false          保留消息，默认false, 非必填\n// 返回类型:object\n// 描述:通过已连接MQTT服务器发送一条消息,返回Object\nvar status = mqtt.Publish('/pub/online','这是我要发送的消息内容');\nreturn status;"
            },
            new SysScript
            {
                Id = 437556098965709, Name = "断开连接", Content = "mqtt.DisConnect();", ScriptType = SysScriptTypeEnum.Mqtt,
                Describe =
                    "// 方法:mqtt.DisConnect()\n// 返回类型:无\n// 描述:断开当前MQTT连接,无返回\nmqtt.DisConnect();"
            },

            #endregion MQTT
            
            #region 文件操作

            #region 本地文件

            new SysScript
            {
                Id = 437556098965710, Name = "获取文件列表", Content = "file.List('src','/usr/local/');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.List()\r\n// 参数一: 'status'  文件/目录名称, \r\n// 参数二: '/usr/local/' 路径\r\n// 返回类型:object\r\n// 描述:获取文件列表,返回object\r\nvar files = file.List('src','/usr/local/');\r\nreturn files;"
            },

            new SysScript
            {
                Id = 437556098965711, Name = "移动文件目录", Content = "file.Move('test.txt', '/usr/local/src/','/usr/local/src/aa/');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.Move()\r\n// 参数一: 'test.txt'  文件/目录名称,\r\n// 参数二: '/usr/local/src/' 原文件所在路径\r\n// 参数三: '/usr/local/src/aa/' 文件移动后新的路径\r\n// 返回类型:bool,失败抛出异常\r\n// 描述:移动文件目录,返回bool\r\nvar isOk = file.Move('test.txt', '/usr/local/src/','/usr/local/src/aa/');\r\nreturn isOk;"
            },
            new SysScript
            {
                Id = 437556098965712, Name = "删除文件目录", Content = "file.Remove('test.txt', '/usr/local/src/');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.Remove()\r\n// 参数一: 'test.txt'  文件/目录名称,\r\n// 参数二: '/usr/local/src/' 文件所在路径\r\n// 返回类型:bool,失败抛出异常\r\n// 描述:删除文件目录,返回bool\r\nvar isDel = file.Remove('test.txt', '/usr/local/src/');\r\nreturn isDel;"
            },

            new SysScript
            {
                Id = 437556098965713, Name = "创建文件夹", Content = "file.CreateDir('dirTest', '/usr/local/src/');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.CreateDir()\r\n// 参数一: 'dirTest'  文件/目录名称,\r\n// 参数二: '/usr/local/src/' 目录所在路径\r\n// 返回类型:bool,失败抛出异常\r\n// 描述:创建文件夹,返回bool\r\nvar isCreate = file.CreateDir('dirTest', '/usr/local/src/');\r\nreturn isCreate;"
            },

            new SysScript
            {
                Id = 437556098965714, Name = "创建文件", Content = "file.CreateFile('test.txt', '/usr/local/src/','hello word',true);", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.CreateFile()\r\n// 参数一: 'test.txt'  文件名称,\r\n// 参数二: '/usr/local/src/' 目录所在路径\r\n// 参数三: 'hello word' test.txt文件内容\r\n// 参数四: 'true' 是否追加一行写入,默认true，设置为false将覆盖式写入\r\n// 返回类型:bool,失败抛出异常\r\n// 描述:创建文件并写入内容,返回bool\r\nvar isCreate = file.CreateFile('test.txt', '/usr/local/src/','hello word',true);\r\nreturn isCreate;"
            },

            new SysScript
            {
                Id = 437556098965715, Name = "读取文本内容", Content = "file.ReadText('test.txt', '/usr/local/src/','utf-8');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.ReadText()\r\n// 参数一: 'test.txt'  文件名称,\r\n// 参数二: '/usr/local/src/' 目录所在路径\r\n// 参数三: 'utf-8' 非必填项,默认'utf-8' 设置为其他则返回bytes\r\n// 返回类型:object\r\n// 描述:读取文本内容,返回object\r\nvar content = file.ReadText('test.txt', '/usr/local/src/','utf-8');\r\nreturn content;"
            },

            new SysScript
            {
                Id = 437556098965716, Name = "下载文件", Content = "file.Download('test.txt', '/usr/local/src/','url','utf-8');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:file.Download()\r\n// 参数一: 'test.txt'  文件名称,\r\n// 参数二: '/usr/local/src/' 目录所在路径\r\n// 参数三: 'url'  下载地址\r\n// 参数四: 'utf-8'  编码格式默认utf-8\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回bool\r\nvar isDown = file.Download('test.txt', '/usr/local/src/','url');\r\nreturn isDown;"
            },

            #endregion
            
            #region SMB

            new SysScript
            {
                Id = 437556098965717, Name = "共享文件-获取文件列表", Content = "smb.List('127.0.0.1', '目录名称','userName','password','子目录名称',1);", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:smb.List()\r\n// 参数一: '127.0.0.1'  ip\r\n// 参数二: '/usr/local/src/' 目录所在路径\r\n// 参数三: 'userName'  用户名\r\n// 参数四: 'password'  密码\r\n// 参数五: '子目录名称'  没有写空\r\n// 参数六: '1'  共享文件协议版本,支持1，2，3\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回List\r\nvar files = smb.List(ip, path, userName, password, '/Data/Files',1)\r\nreturn files;"
            },

            new SysScript
            {
                Id = 437556098965718, Name = "共享文件-下载文件到本地", Content = "smb.Download(ip, path, userName, password, fileName, '/disk1/windows/','子目录名称')", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:smb.Download()\r\n// 参数一: 'ip'  ip\r\n// 参数二: 'path' 目录所在路径\r\n// 参数三: 'userName'  用户名\r\n// 参数四: 'password'  密码\r\n// 参数五: 'fileName'  文件名称\r\n// 参数六: '/disk1/windows/'  下载到本地路径\r\n// 参数七: '子目录名称'  子目录名称\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回bool\r\nvar isDown = smb.Download(ip, path, userName, password, fileName, '/disk1/windows/','');\r\nreturn isDown;"
            },
              new SysScript
            {
                Id = 437556098932177, Name = "共享文件-下载文件到本地", Content = "smb.DownloadFolder(ip, path, userName, password, fileName, '/disk1/windows/','忽略文件集合')", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:smb.DownloadFolder()\r\n// 参数一: 'ip'  ip\r\n// 参数二: 'path' 目录所在路径\r\n// 参数三: 'userName'  用户名\r\n// 参数四: 'password'  密码\r\n// 参数五: 'fileName'  文件名称\r\n// 参数六: '/disk1/windows/'  下载到本地路径\r\n// 参数七: '忽略文件集合'  忽略文件集合\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回bool\r\nvar isDown = smb.DownloadFolder(ip, path, userName, password, fileName, '/disk1/windows/','');\r\nreturn isDown;"
            },

            #endregion

            #region Ftp

            new SysScript
            {
                Id = 437556098965719, Name = "ftp文件列表", Content = "ftp.List('ftp://************', 'userName','password');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:ftp.List()\r\n// 参数一: 'ftp://************'  ftp地址,\r\n// 参数二: 'userName' 用户名称\r\n// 参数三: 'password'  密码\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回集合对象\r\nvar list = ftp.List('ftp://************, 'userName','password');\r\nreturn list;"
            },

            new SysScript
            {
                Id = 437556098965720, Name = "ftp下载文件", Content = "ftp.Download('ftp://************/test.txt', '/usr/local/src/','userName','password');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:ftp.Download()\r\n// 参数一: 'ftp://************/test.txt'  ftp远程地址,\r\n// 参数二: '/usr/local/src/' 下载到本地路径\r\n// 参数三: 'userName'  用户名称\r\n// 参数四: 'password'  密码\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回bool\r\nvar isDown = ftp.Download('ftp://************/test.txt', '/usr/local/src/','userName','password');\r\nreturn isDown;"
            },

            new SysScript
            {
                Id = 437556098965721, Name = "ftp上传文件", Content = "ftp.Upload('ftp://************/home/', '/usr/local/src/test.txt','userName','password');", ScriptType = SysScriptTypeEnum.File,
                Describe =
                    "// 方法:ftp.Download()\r\n// 参数一: 'ftp://************/version.txt'  ftp远程地址+存储文件名称,\r\n// 参数二: '/usr/local/src/' 下载到本地路径\r\n// 参数三: 'userName'  用户名称\r\n// 参数四: 'password'  密码\r\n// 返回类型:bool 失败抛出异常\r\n// 描述:下载文件,返回bool\r\nvar isDown = ftp.Upload('ftp://************/version.txt', '/usr/local/src/version.txt','user1','123456');\r\nreturn isDown;"
            },

            #endregion
            
            #endregion 文件操作
            
            #region 示例代码

            new SysScript
            {
                Id = 326762076807365, Name = "设备在线状态",
                Content =
                    "// 获取当前设备的连接状态,返回Bool值\nvar status = device.Status('${this.DeviceName}');\nif (status) {\n  // 如果status == true 代表在线,则返回2(根据实际情况调整)\n  return 2;\n} else {\n  // 假设1代表关机\n  return 1;\n}",
                ScriptType = SysScriptTypeEnum.Template, Describe = "当前设备的连接状态,示例 在线返回2;关机则返回1", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807366, Name = "当前设备产量",
                Content = "// 获取当前设备标识为'product' 的值\nvar product = device.Get('${this.DeviceName}.product');\nreturn  product;",
                ScriptType = SysScriptTypeEnum.Template, Describe = "当前设备产量,前置条件需配置`product`属性,否则返回null ", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807367, Name = "今日设备空闲时间累计",
                Content =
                    "//该脚本用于统计当日设备空闲时间累计\n//持久化的累计空闲时间\nvar totalTime = share.Get('${this.DeviceName}_TotalIdleTime') || 0;\n//当前设备连接状态\nvar runStatus = device.Status('${this.DeviceName}');\n//当前设备标识运行状态的属性\nvar status = device.GetData('${this.DeviceName}.runstatus');\nif (status == null) {\n  return totalTime;\n}\n\n//设备在线 && 设备当前状态和上次状态一致才累计时间 && 设备状态是空闲（此处空闲状态表示1,请根据实际情况调整）\nif (runStatus && status.Value == status.CookieValue && status.Value == 1) {\n  \n  //累计时间= 上一次累计时间+(本次运行状态的读取时间-上一次状态的读取时间)\n  totalTime = parseInt(totalTime) + parseInt(status.ReadTime - status.CookieTime);\n  //持久化保存，并且设置过期时间\n  share.Save('${this.DeviceName}_TotalIdleTime', totalTime, '00:00:00');\n}\n//返回统计结果\nreturn totalTime;",
                ScriptType = SysScriptTypeEnum.Template, Describe = "今日设备空闲时间累计,前置条件需配置`runstatus`属性", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807368, Name = "统计当日设备产量",
                Content =
                    "//统计当日设备产量-考虑清空\n\n//获取当前设备持久化的累计产量值\nvar nowProdcut = share.Get('${this.DeviceName}_nowProdcut');\nif (nowProdcut == null) {\n  //如果是null值 默认设置初始化的值\n  share.Save('${this.DeviceName}_nowProdcut', 0, '00:00:00');\n  //初始化产量0\n  nowProdcut = 0;\n}\n//将产量转换成int类型,方便后续计算\nnowProdcut = parseInt(nowProdcut);\n\n//获取当前设备实时产量,注:``当前设备表示总产量的字段是`product` 请根据实际情况进行调整\nvar readPrudct = device.GetData('${this.DeviceName}.product');\n//如果取不到设备的产量属性 就直接返回\nif (readPrudct == null) {\n  return nowProdcut;\n}\n//将设备本次读取到的产量值转换成int\nvar thisProduct = parseInt(readPrudct.Value);\n//设备开机第一次读取,没有上一次的值的情况处理\nif (readPrudct.CookieValue == null) {\n  //获取持久化的最后一次读取的产量值\n  var lastProdcut = parseInt(share.Get('${this.DeviceName}_lastProdcut'));\n  //这里判断是否清空过\n  if (thisProduct > lastProdcut) {\n    //计算差值并累计\n    nowProdcut += thisProduct - lastProdcut;\n  }\n}\n//产量值发生改变,就进行计算\nelse if (readPrudct.CookieValue != readPrudct.Value) {\n  //转换上一次的值\n  var cookieValue = parseInt(readPrudct.CookieValue);\n  //如果当前值小于上一次的值= 设备产量已清空\n  if (thisProduct < cookieValue) {\n    //累计产量+ 本次读取到的产量\n    nowProdcut += thisProduct;\n  } else {\n    //未清空的情况，正常计算产量差值\n    //本次读取到的产量 - 上次读到的产量 = 差值产量\n    var diffProduct = (thisProduct - cookieValue);\n    //累计产量 + 差值产量\n    nowProdcut += diffProduct;\n  }\n}\n//实时保存最后一次读到的产量\nif (readPrudct.Value != null) {\n  share.Save('${this.DeviceName}_lastProdcut', thisProduct);\n}\n//持久化保存累计产量\nshare.Save('${this.DeviceName}_nowProdcut', nowProdcut, '00:00:00');\nreturn nowProdcut;",
                ScriptType = SysScriptTypeEnum.Template, Describe = "统计当日设备产量-考虑清空的情况,前置条件需配置`product`属性", Method = ScriptMethodTypeEnum.Template
            },
            new SysScript
            {
                Id = 326762076807369, Name = "计算OEE",
                Content =
                    "//脚本示例了OEE的计算\n//前置条件,需要当前设备 空闲时间/运行时间/报警时间 统计的属性字段\n\n//注:以下字段,请根据实际命名调整\n\n//空闲时间\nvar idleTime = device.Get('${this.DeviceName}.TodayDeviceIdleTimeAccumulation');\n//运行时间\nvar runTime = device.Get('${this.DeviceName}.TodayDeviceRunTimeAccumulation');\n//报警时间\nvar alarmTime = device.Get('${this.DeviceName}.TodayDeviceAlarmTimeAccumulation');\n\nvar totalTime = parseInt(idleTime) + parseInt(runTime) + parseInt(alarmTime);\n//除数不能为0\nif (totalTime == 0) {\n  return 0;\n}\n//OEE 计算公式 = 运行时间/（运行+空闲+报警） * 100\nreturn runTime / totalTime * 100;",
                ScriptType = SysScriptTypeEnum.Template, Describe = "统计设备OEE,前置条件需配置`空闲时间/运行时间/报警时间`属性", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807370, Name = "串口连接",
                Content =
                    "//示例:演示如何在脚本中调用串口函数\r\n//打开串口\r\nvar isOpen = serial.OpenCom('/dev/ttyS1', 9600, 8, 1, 0);\r\n//判断串口是否正确打开\r\nif (isOpen) {\r\n  //定义一个返回结果参数，可以根据实际情况定义，这里仅作为演示\r\n  var output;\r\n  //通过串口发送消息，根据实际情况可以调用系统函数对实际的消息内容进行处理\r\n  //该方法有重载方式，支持设置发送格式，返回报文格式以及分断收包...具体可查看方法说明\r\n  var data = serial.SendCom('01 03 00 00 00 01 84 0A');\r\n  //如果有内容返回\r\n  if (data != null) {\r\n    //将返回的原始字节数组转换成16进制字符串\r\n    var byteList = system.ByteToHexString(data, 'array');\r\n    //根据实际协议规则 将实际的值转换成对应的数据类型\r\n    output = system.BytesToString('int16', byteList[4] + byteList[3]);\r\n  } else {\r\n    output = \"发送失败\"\r\n  }\r\n  //关闭串口方法，根据实际情况使用，目前任务结束自动关闭\r\n  // serial.CloseCom();\r\n  return output;\r\n} else {\r\n  return \"串口未打开\"\r\n}",
                ScriptType = SysScriptTypeEnum.Template, Describe = "调用串口函数,演示如何在脚本中调用串口的连接,发送,接收,关闭等函数的正确使用方式", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807371, Name = "tcp连接",
                Content =
                    "// 示例:通过tcp自定义连接发送消息\r\nvar isConnect = tcp.Connect('************', 503);\r\n// 返回bool值，判断是否连接成功\r\nif (!isConnect) {\r\n  // 连接失败直接退出\r\n  return \"连接失败\"\r\n} else {\r\n  // 向服务端发送数据\r\n  var isSend = tcp.Send('12345678', 'ascii', 'binary');\r\n  // 返回object值,null值是没有收到返回内容或者发送失败\r\n  if (isSend == null) {\r\n    // 发送失败逻辑..\r\n    return \"没有收到返回内容或者发送失败\";\r\n  } else {\r\n    // 返回收到的报文\r\n    return isSend;\r\n  }\r\n}",
                ScriptType = SysScriptTypeEnum.Template, Describe = "调用tcp函数,演示如何在脚本中调用串口的连接,发送,关闭等函数的正确使用方式", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807372, Name = "udp连接",
                Content =
                    "// 示例:通过udp自定义连接发送消息\r\nvar isConnect = udp.Connect('************', 503);\r\n// 返回bool值，判断是否连接成功(UDP是面向无连接的协议,不存在连接概念默认返回true)\r\nif (!isConnect) {\r\n  // 连接失败直接退出\r\n  return \"连接失败\"\r\n} else {\r\n  // 向服务端发送数据\r\n  var isSend = udp.Send('12345678', 'ascii', 'binary');\r\n  // 返回object值,null值是没有收到返回内容或者发送失败\r\n  if (isSend == null) {\r\n    // 发送失败逻辑..\r\n    return \"没有收到返回内容或者发送失败\";\r\n  } else {\r\n    // 返回收到的报文\r\n    return isSend;\r\n  }\r\n}\r\n",
                ScriptType = SysScriptTypeEnum.Template, Describe = "调用udp函数,演示如何在脚本中调用udp的连接,发送等函数的正确使用方式", Method = ScriptMethodTypeEnum.Template
            },

            new SysScript
            {
                Id = 326762076807373, Name = "MQTT连接",
                Content =
                    "// 示例:通过MQTT连接并且发送主题消息\n// 连接MQTT服务端\nvar isConnect = mqtt.Connect('127.0.0.1',1883,'clientid');\n// 成功连接\nif (isConnect) {\n  // 发送一条主题消息\n  var pub = mqtt.Publish('/pub/test','连接成功后发送一条消息');\n  // 检查是否发送成功\n  if (pub.success == true) {\n    // 发送成功逻辑\n    log.Write('发送成功啦');\n  }else{\n    // 失败逻辑\n    log.Write('oops.=_=# 发送失败');\n  }\n}\n// 主动断开连接\nmqtt.DisConnect();\nreturn '执行完毕';",
                ScriptType = SysScriptTypeEnum.Template, Describe = "通过脚本连接MQTT服务端并且进行主题消息发布", Method = ScriptMethodTypeEnum.Template
            },


            new SysScript
            {
                Id = 326762076807374, Name = "中移平台实时数据上报",
                Content =
                    "var data = payload;\n\n//根据网关侧数据类型转换成研华平台需要的数据值\nfunction getValue(object) {\n  switch (object.ValueType) {\n    case 'Int':\n      return parseInt(object.Value);\n    case 'Double':\n      return parseFloat(object.Value);\n    default:\n      return object.Value;\n  }\n}\n\n//属性配置\nvar tagValue = {};\n\nfor (var key in data.Params) {\n  //循环转换数据值\n  tagValue[key] = getValue(data.Params[key]);\n}\n\nvar pubData = {\n  \"et\": dateTime.Now('cst'),\n  \"da\": [{\n    'id': data.DeviceName,\n    'da': tagValue\n  }]\n}\n\nreturn JSON.stringify(pubData);",
                ScriptType = SysScriptTypeEnum.Template, Describe = "【实时数据上报】 该功能用于对接中移平台，解析格式按照要求格式提供", Method = ScriptMethodTypeEnum.Template
            },


            new SysScript
            {
                Id = 326762076807375, Name = "中移平台心跳上报",
                Content =
                    "/**\n * 将设备自定义topic数据转换为json格式数据, 设备上报数据到物联网平台时调用\n * 入参：rawData 发送源数据对象 不能为空\n * 出参：jsonObj JSON 对象 不能为空\n */\nvar deviceConnectList = device.Status();\nvar daList = [];\nfor (var deviceConnect of deviceConnectList) {\n  var da = {};\n  da['id'] = deviceConnect.DeviceName;\n  da['ds'] = deviceConnect.OnLine == true ? 0 : 1;\n  daList.push(da);\n}\n\nvar payload = {\n  'ip': '***********',\n  'et': dateTime.Now('cst'),\n  'da': daList\n}\nreturn JSON.stringify(payload);",
                ScriptType = SysScriptTypeEnum.Template, Describe = "【心跳上报】 该功能用于对接中移平台，解析格式按照要求格式提供", Method = ScriptMethodTypeEnum.Template
            },

            #endregion 示例代码
        };
    }
}