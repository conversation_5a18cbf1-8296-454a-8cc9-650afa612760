using DongleKeyManager.Core.Entities;
using DongleKeyManager.Core.SqlSugar;
using System.Security.Cryptography;
using System.Text;

namespace DongleKeyManager.Core.SeedData;

/// <summary>
/// 用户种子数据
/// </summary>
public class SysUserSeedData : ISqlSugarEntitySeedData<User>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<User> HasData()
    {
        // 超级管理员
        var superAdminSalt = GenerateSalt();
        var superAdminPasswordHash = GeneratePasswordHash("fengedge", superAdminSalt);
        yield return new User
        {
            Id = 1000001,
            Username = "superAdmin",
            PasswordHash = superAdminPasswordHash,
            Salt = superAdminSalt,
            Role = UserRole.SuperAdmin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认超级管理员账户"
        };

        // 管理员
        var adminSalt = GenerateSalt();
        var adminPasswordHash = GeneratePasswordHash("admin123", adminSalt);
        yield return new User
        {
            Id = 1000002,
            Username = "admin",
            PasswordHash = adminPasswordHash,
            Salt = adminSalt,
            Role = UserRole.Admin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认管理员账户，默认密码：admin123"
        };

    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    private static string GenerateSalt(int length = 16)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var salt = new char[length];

        for (int i = 0; i < length; i++)
        {
            salt[i] = chars[random.Next(chars.Length)];
        }

        return new string(salt);
    }

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    private static string GeneratePasswordHash(string password, string salt)
    {
        var saltedPassword = password + salt;
        using var sha256 = SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(saltedPassword);
        var hashBytes = sha256.ComputeHash(inputBytes);

        return Convert.ToBase64String(hashBytes);
    }
}