namespace Feng.IotGateway.Core.SeedData;

/// <summary>
///     采集协议种子数据
/// </summary>
public class SysDictDataSeedData : ISqlSugarEntitySeedData<SysDictData>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysDictData> HasData()
    {
        var sysDictData = new List<SysDictData>();

        #region Fanuc

        // Fanuc
        sysDictData.AddRange(CreateCncSeedData(1423070709139001));
        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139001, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "text", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "R", "Macro"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139001, Value = "",
                Code = DeviceConst.Length, Name = "读取长度", Type = "text", DefaultValue = "10", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "R"
                        }
                    }
                }.ToJson()
            }
        });

        //Fanuc18i 
        sysDictData.AddRange(CreateCncSeedData(1423070709139002));

        //FanucTtc
        sysDictData.AddRange(CreateCncSeedData(1523070709139040));
        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139040, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "text", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "R"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139040, Value = DeviceConst.CncReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "R"
                        }
                    }
                }.ToJson()
            }
        });

        //FanucMultiStation
        sysDictData.AddRange(CreateCncSeedData(1423070709139003));

        #endregion Fanuc

        #region 新代(Syntec)

        //SyntecV4
        sysDictData.AddRange(CreateCncSeedData(1423070709139005));

        //SyntecV2
        sysDictData.AddRange(CreateCncSeedData(1423070709139006));

        //SyntecV3
        sysDictData.AddRange(CreateCncSeedData(1423070709139007));

        //Syntec6MA
        sysDictData.AddRange(CreateCncSeedData(1523070709139045));

        //Syntec6AE
        sysDictData.AddRange(CreateCncSeedData(1523070709139018));

        #endregion 新代(Syntec)

        #region 广州数控(GSK)

        //Gsk25im
        sysDictData.AddRange(CreateCncSeedData(1423070709139009));

        //GskTcp
        sysDictData.AddRange(CreateCncSeedData(1423070709139010));

        //GskUdp
        sysDictData.AddRange(CreateCncSeedData(1423070709139011));

        #endregion 广州数控(GSK)

        #region 凯恩蒂(Knd)

        //SysDictData
        sysDictData.AddRange(CreateCncSeedData(1423070709139012));

        #endregion 凯恩蒂(Knd)

        #region 马扎克(Mazak)

        //MazakMatrix
        sysDictData.AddRange(CreateCncSeedData(1523070709139036));
        //MazakSmooth
        sysDictData.AddRange(CreateCncSeedData(1523070709139037));
        //MazakSmart
        sysDictData.AddRange(CreateCncSeedData(1523070709139044));

        #endregion

        #region 海德汉(Heidenhain)

        //MazakSmart
        sysDictData.AddRange(CreateCncSeedData(1523070709139039));

        #endregion 海德汉(Heidenhain)

        #region 宝元(Lnc)

        //Lnc
        sysDictData.AddRange(CreateCncSeedData(1523070709139042));

        #endregion 宝元(Lnc)

        #region 法格

        //fagornet
        sysDictData.AddRange(CreateCncSeedData(1523070709139046));

        #endregion 法格

        #region 华中

        //华中
        sysDictData.AddRange(CreateCncSeedData(1523070709139047));

        #endregion 华中

        #region Modbus

        //ModbusRtuOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139019, DeviceConst.BcdReadDataTypeValue));

        //ModbusUdpNet
        sysDictData.AddRange(CreatePlcSeedData(1523070709139020, DeviceConst.BcdReadDataTypeValue));

        //ModbusAsciiOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139021, DeviceConst.BcdReadDataTypeValue));

        //ModbusTcp
        sysDictData.AddRange(CreatePlcSeedData(1423070709139000, DeviceConst.BcdReadDataTypeValue));

        //ModbusRtu
        sysDictData.AddRange(CreatePlcSeedData(1523070709139000, DeviceConst.BcdReadDataTypeValue));

        //ModbusAscii
        sysDictData.AddRange(CreatePlcSeedData(1523070709139003, DeviceConst.BcdReadDataTypeValue));

        #endregion Modbus

        #region 西门子(Siemens)

        //Siemens_S7
        sysDictData.AddRange(CreatePlcSeedData(1523070709139013));

        //SiemensPPI
        sysDictData.AddRange(CreatePlcSeedData(1523070709139014));

        //Siemens_CNC
        sysDictData.AddRange(CreateCncSeedData(1423070709139004));
        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139004, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "text", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "ReadPlc","R"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139004, Value = DeviceConst.BcdReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "Method", new List<string>
                        {
                            "ReadPlc"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139004, Value = DeviceConst.EncodingValue,
                Code = DeviceConst.Encoding, Name = "编码", DefaultValue = "1", Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1423070709139004, Value = "",
                Code = DeviceConst.Length, Name = "读取长度", Type = "text", DefaultValue = "10", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            }
        });

        #endregion 西门子(Siemens)

        #region 欧姆龙PLC(Omron)

        //FinsUdp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139010, DeviceConst.BcdReadDataTypeValue));

        //FinsNet
        sysDictData.AddRange(CreatePlcSeedData(1523070709139009, DeviceConst.BcdReadDataTypeValue));

        //OmronCipNet
        sysDictData.AddRange(CreatePlcSeedData(1523070709139011, DeviceConst.BcdReadDataTypeValue));

        //HostLink
        sysDictData.AddRange(CreatePlcSeedData(1523070709139034, DeviceConst.BcdReadDataTypeValue));

        //HostLinkOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139035, DeviceConst.BcdReadDataTypeValue));

        #endregion 欧姆龙PLC(Omron)

        #region 罗克韦尔(allen_bradley)

        //AllenBradleyCip
        sysDictData.AddRange(CreatePlcSeedData(1523070709139008));

        //AllenBradleyPccc
        sysDictData.AddRange(CreatePlcSeedData(1523070709139050));

        #endregion 罗克韦尔(allen_bradley)

        #region 三菱(Melsec)

        //McUdp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139007));

        //McQna3E
        sysDictData.AddRange(CreatePlcSeedData(1523070709139006));

        //McFxLinks
        sysDictData.AddRange(CreatePlcSeedData(1523070709139005));

        // McA1E
        sysDictData.AddRange(CreatePlcSeedData(1523070709139004));

        // Mitsub-cnc
        sysDictData.AddRange(CreatePlcSeedData(1423070709139008));

        // MelsecA1EAscii
        sysDictData.AddRange(CreatePlcSeedData(1523070709139041));


        // McFxSerial
        sysDictData.AddRange(CreatePlcSeedData(DriverId.McFxSerial));

        #endregion 三菱(Melsec)

        #region 台达(Delta)

        // DeltaSerialAsciiOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139053));

        // DeltaSerialAscii
        sysDictData.AddRange(CreatePlcSeedData(1523070709139002));

        // DeltaTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139001));

        #endregion 台达(Delta)

        #region 松下PLC(Panasonic)

        // MewtocolOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139012));

        #endregion 松下PLC(Panasonic)

        #region 标准协议

        // MtConnectClient
        sysDictData.AddRange(CreatePlcSeedData(1523070709139016));

        #region OpcUaClient

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139015, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139015, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139015, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "combine"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139015, Value = DeviceConst.ReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4"
            }
        });

        #endregion OpcUaClient

        #region OpcDaClient

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139048, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139048, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139048, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "combine"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139048, Value = DeviceConst.ReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4"
            }
        });

        #endregion OpcDaClient


        #region TcpClient

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139026, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139026, Value = DeviceConst.TcpClientReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "11"
            }
        });

        #endregion TcpClient

        #region SerialClient

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.SerialClient, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.SerialClient, Value = "", Code = DeviceConst.RegisterAddress,
                Name = "发送指令", Type = "text",Required = false
            },
        });

        #endregion SerialClient

        #endregion 标准协议

        #region 信捷

        //XinJE TcpNet(ModBus)
        sysDictData.AddRange(CreatePlcSeedData(1523070709139022));

        //XinJESerialOverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139023));

        //XinJESerial
        sysDictData.AddRange(CreatePlcSeedData(1523070709139024));

        //XinJE TcpNet(专用)
        sysDictData.AddRange(CreatePlcSeedData(1523070709139025));

        #endregion 信捷

        #region InovanceTcp

        //InovanceTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139033));

        #endregion InovanceTcp

        #region Dlt

        //Dlt645
        sysDictData.AddRange(CreatePlcSeedData(1523070709139028));

        //Dlt645OverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139029));

        //Dlt698
        sysDictData.AddRange(CreatePlcSeedData(1523070709139030));

        //Dlt698OverTcp
        sysDictData.AddRange(CreatePlcSeedData(1523070709139031));

        //Dlt698TcpNet
        sysDictData.AddRange(CreatePlcSeedData(1523070709139032));

        #endregion Dlt

        #region Snmp

        sysDictData.AddRange(CreatePlcSeedData(1523070709139038));

        #endregion Snmp

        #region MqttServer

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "Topic", Type = "text"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = DeviceConst.ReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = DeviceConst.EncodingValue,
                Code = DeviceConst.Encoding, Name = "编码", DefaultValue = "1", Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServer, Value = "",
                Code = DeviceConst.Length, Name = "读取长度", Type = "text", DefaultValue = "10", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            }
        });

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "Topic", Type = "text"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = DeviceConst.ReadDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = DeviceConst.EncodingValue,
                Code = DeviceConst.Encoding, Name = "编码", DefaultValue = "1", Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.MqttServerEx, Value = "",
                Code = DeviceConst.Length, Name = "读取长度", Type = "text", DefaultValue = "10", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            }
        });

        #endregion MqttServer

        #region 注塑机

        #region 宝捷信（PORCHESON_Ps660Am）

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139054, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139054, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion 宝捷信（PORCHESON_Ps660Am）

        #region 宝捷信（PORCHESON_Ps660Bm）

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139051, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = 1523070709139051, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion 宝捷信（PORCHESON_Ps660Bm）

        #region 弘讯（TechMationAk）

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.TechMationAk, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.TechMationAk, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion 弘讯（TechMationAk）

        #region 弘讯（HongXunAkDouble）

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.HongXunAkDouble, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.HongXunAkDouble, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion 弘讯（HongXunAkDouble）
        #region 弘讯（TechMation5521）

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.TechMation5521, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.TechMation5521, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion 弘讯（TechMation5521）

        #region 科强（KeQiang）

        #region KeQiang_T6H3

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.KeQiangT6H3, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.KeQiangT6H3, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion

        #region KeQiang_T6F3

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.KeQiangT6F3, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.KeQiangT6F3, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion

        #endregion 科强（KeQiang）

        #region 卷绕机

        #region WarpingMachine

        sysDictData.AddRange(new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.WarpingMachine, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = DriverId.WarpingMachine, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        });

        #endregion

        #endregion 卷绕机

        #endregion 注塑机

        #region 基恩士(Keyence)

        // McNet
        sysDictData.AddRange(CreatePlcSeedData(DriverId.McNet, DeviceConst.BcdReadDataTypeValue));

        // McAsciiNet
        sysDictData.AddRange(CreatePlcSeedData(DriverId.McAsciiNet, DeviceConst.BcdReadDataTypeValue));

        // NanoSerial
        sysDictData.AddRange(CreatePlcSeedData(DriverId.NanoSerial, DeviceConst.BcdReadDataTypeValue));

        // NanoSerialOverTcp
        sysDictData.AddRange(CreatePlcSeedData(DriverId.NanoSerialOverTcp, DeviceConst.BcdReadDataTypeValue));
        #endregion 基恩士(Keyence)

        #region 永宏(Fatek) 

        // ProgramOverTcp
        sysDictData.AddRange(CreatePlcSeedData(DriverId.ProgramOverTcp, DeviceConst.BcdReadDataTypeValue));

        #endregion

        #region 兄弟(Brother)

        sysDictData.AddRange(CreateCncSeedData(DriverId.Brother));

        #endregion

        #region 转发数据类型

        sysDictData.AddRange(new List<SysDictData>
        {
            new() {Id = 142307070998430, DictTypeId = 142307070906001, Value = "1", Code = "bool", Name = "Bool"},
            new() {Id = 142307070998431, DictTypeId = 142307070906001, Value = "2", Code = "long", Name = "Int"},
            new() {Id = 142307070998432, DictTypeId = 142307070906001, Value = "3", Code = "double", Name = "Double"},
            new() {Id = 142307070998433, DictTypeId = 142307070906001, Value = "4", Code = "string", Name = "String"},
            new() {Id = 142307070998434, DictTypeId = 142307070906001, Value = "5", Code = "bytes", Name = "Bytes"}
        });

        #endregion 转发数据类型

        #region SQL变量

        sysDictData.AddRange(new List<SysDictData>
        {
            new() {Id = 1423070709050011, DictTypeId = 142307070905001, Value = "$guid", Code = "GUID", Name = "GUID"},
            new() {Id = 1423070709050012, DictTypeId = 142307070905001, Value = "$value", Code = "值", Name = "值"},
            new()
            {
                Id = 1423070709050013, DictTypeId = 142307070905001, Value = "$yyyymm", Code = "年月(yyyymm)",
                Name = "年月(yyyymm)"
            },
            new()
            {
                Id = 1423070709050014, DictTypeId = 142307070905001, Value = "$yymm", Code = "年月(yymm)",
                Name = "年月(yymm)"
            },
            new() {Id = 1423070709050015, DictTypeId = 142307070905001, Value = "$key", Code = "属性标识", Name = "属性标识"},
            new()
            {
                Id = 1423070709050016, DictTypeId = 142307070905001, Value = "$deviceName", Code = "设备名称", Name = "设备名称"
            },
            new() {Id = 1423070709050017, DictTypeId = 142307070905001, Value = "$timestamp", Code = "时间戳", Name = "时间戳"},
            new() {Id = 1423070709050018, DictTypeId = 142307070905001, Value = "$time", Code = "当前时间", Name = "当前时间"},
            new()
            {
                Id = 1423070709050019, DictTypeId = 142307070905001, Value = "$deviceStatusName", Code = "设备连接状态(中文)",
                Name = "设备连接状态(中文)"
            },
            new()
            {
                Id = 1423070709050020, DictTypeId = 142307070905001, Value = "$deviceStatus", Code = "设备连接状态",
                Name = "设备连接状态"
            },
            new()
            {
                Id = 1423070709050021, DictTypeId = 142307070905001, Value = "$deviceVariableName", Code = "属性名称",
                Name = "属性名称"
            }
        });

        #endregion SQL变量

        return sysDictData;
    }

    /// <summary>
    ///     生成Cnc基础数据
    /// </summary>
    /// <param name="dictTypeId"></param>
    /// <returns></returns>
    private IEnumerable<SysDictData> CreateCncSeedData(long dictTypeId)
    {
        return new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = DeviceConst.Read,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            }
        };
    }

    /// <summary>
    ///     生成Plc基础数据
    /// </summary>
    /// <param name="dictTypeId"></param>
    /// <param name="readDataTypeValue"></param>
    /// <returns></returns>
    private IEnumerable<SysDictData> CreatePlcSeedData(long dictTypeId, string readDataTypeValue = DeviceConst.ReadDataTypeValue)
    {
        return new List<SysDictData>
        {
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = DeviceConst.ProtectTypeValue,
                Code = DeviceConst.ProtectType, Name = "读写权限", DefaultValue = "1"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = DeviceConst.Value,
                Code = DeviceConst.Method, Name = "读取方法"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = "0", Code = DeviceConst.RegisterAddress,
                Name = "读取地址", Type = "text"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = readDataTypeValue,
                Code = DeviceConst.ReadDataType, Name = "数据类型", DefaultValue = "4"
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = DeviceConst.EncodingValue,
                Code = DeviceConst.Encoding, Name = "编码", DefaultValue = "1", Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            },
            new()
            {
                Id = YitIdHelper.NextId(), DictTypeId = dictTypeId, Value = "",
                Code = DeviceConst.Length, Name = "读取长度", Type = "text", DefaultValue = "10", Required = false, Display = false, DisplayExpress = new Dictionary<string, List<string>>
                {
                    {
                        "DataType", new List<string>
                        {
                            "11"
                        }
                    }
                }.ToJson()
            }
        };
    }
}