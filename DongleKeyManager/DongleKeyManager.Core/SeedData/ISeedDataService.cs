namespace DongleKeyManager.Core.SeedData;

/// <summary>
/// 种子数据服务接口
/// </summary>
public interface ISeedDataService
{
    /// <summary>
    /// 初始化所有种子数据
    /// </summary>
    /// <returns></returns>
    Task InitializeAllSeedDataAsync();

    /// <summary>
    /// 初始化用户种子数据
    /// </summary>
    /// <returns></returns>
    Task InitializeUserSeedDataAsync();

    /// <summary>
    /// 初始化设备种子数据
    /// </summary>
    /// <returns></returns>
    Task InitializeDeviceSeedDataAsync();

    /// <summary>
    /// 清空所有数据
    /// </summary>
    /// <returns></returns>
    Task ClearAllDataAsync();

    /// <summary>
    /// 重置为初始状态
    /// </summary>
    /// <returns></returns>
    Task ResetToInitialStateAsync();
}
