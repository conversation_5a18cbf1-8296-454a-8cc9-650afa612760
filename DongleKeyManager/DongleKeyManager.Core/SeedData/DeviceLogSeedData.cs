using DongleKeyManager.Core.Entities;
using DongleKeyManager.Core.SqlSugar;

namespace DongleKeyManager.Core.SeedData;

/// <summary>
/// 设备日志种子数据
/// </summary>
public class DeviceLogSeedData : ISqlSugarEntitySeedData<DeviceLog>
{
    /// <summary>
    /// 种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<DeviceLog> HasData()
    {
        var baseTime = DateTime.Now.AddDays(-30);
        var logId = 3000001L;

        // 设备1的操作日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000001,
            OperationType = LogOperationType.Create,
            OperationDescription = "创建设备",
            UserId = 1000002,
            Username = "admin",
            OperationTime = baseTime,
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "系统管理员创建演示设备001"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000001,
            OperationType = LogOperationType.Activate,
            OperationDescription = "激活设备",
            UserId = 1000002,
            Username = "admin",
            OperationTime = baseTime.AddDays(1),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "设备激活成功"
        };

        // 设备验证日志
        for (int i = 0; i < 10; i++)
        {
            yield return new DeviceLog
            {
                Id = logId++,
                DeviceId = 2000001,
                OperationType = LogOperationType.Verify,
                OperationDescription = "设备验证",
                OperationTime = baseTime.AddDays(2 + i * 2),
                ClientIpAddress = "*************",
                UserAgent = "DongleKeyManager-Client/1.0",
                IsSuccess = true,
                Remarks = $"设备验证成功 - 第{i + 1}次"
            };
        }

        // 设备2的操作日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000002,
            OperationType = LogOperationType.Create,
            OperationDescription = "创建设备",
            UserId = 1000002,
            Username = "admin",
            OperationTime = baseTime.AddDays(5),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "系统管理员创建演示设备002"
        };

        // 设备3的操作日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000003,
            OperationType = LogOperationType.Create,
            OperationDescription = "创建设备",
            UserId = 1000002,
            Username = "admin",
            OperationTime = baseTime.AddDays(10),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "系统管理员创建演示设备003"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000003,
            OperationType = LogOperationType.Activate,
            OperationDescription = "激活设备",
            UserId = 1000002,
            Username = "admin",
            OperationTime = baseTime.AddDays(11),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "设备激活成功"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000003,
            OperationType = LogOperationType.Disable,
            OperationDescription = "禁用设备",
            UserId = 1000001,
            Username = "superadmin",
            OperationTime = DateTime.Now.AddDays(-3),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "超级管理员禁用设备"
        };

        // 设备4的操作日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000004,
            OperationType = LogOperationType.Create,
            OperationDescription = "创建设备",
            UserId = 1000003,
            Username = "testuser",
            OperationTime = baseTime.AddDays(15),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "测试用户创建演示设备004"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000004,
            OperationType = LogOperationType.Activate,
            OperationDescription = "激活设备",
            UserId = 1000003,
            Username = "testuser",
            OperationTime = baseTime.AddDays(16),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "设备激活成功"
        };

        // 设备5的操作日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000005,
            OperationType = LogOperationType.Create,
            OperationDescription = "创建设备",
            UserId = 1000004,
            Username = "demo",
            OperationTime = baseTime.AddDays(20),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "演示用户创建演示设备005"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000005,
            OperationType = LogOperationType.Activate,
            OperationDescription = "激活设备",
            UserId = 1000004,
            Username = "demo",
            OperationTime = baseTime.AddDays(21),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            Remarks = "设备激活成功"
        };

        // 高频验证日志（设备5）
        for (int i = 0; i < 20; i++)
        {
            yield return new DeviceLog
            {
                Id = logId++,
                DeviceId = 2000005,
                OperationType = LogOperationType.Verify,
                OperationDescription = "设备验证",
                OperationTime = baseTime.AddDays(22 + i * 0.5),
                ClientIpAddress = "*************",
                UserAgent = "DongleKeyManager-Client/1.0",
                IsSuccess = true,
                ExecutionTime = Random.Shared.Next(50, 200), // 随机执行时间
                Remarks = $"高频设备验证成功 - 第{i + 1}次"
            };
        }

        // 一些失败的验证日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000002, // 未激活设备
            OperationType = LogOperationType.Verify,
            OperationDescription = "设备验证失败",
            OperationTime = DateTime.Now.AddHours(-6),
            ClientIpAddress = "*************",
            UserAgent = "DongleKeyManager-Client/1.0",
            IsSuccess = false,
            ErrorMessage = "设备状态异常：未激活",
            ExecutionTime = 45,
            Remarks = "验证失败：设备未激活"
        };

        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000003, // 已禁用设备
            OperationType = LogOperationType.Verify,
            OperationDescription = "设备验证失败",
            OperationTime = DateTime.Now.AddHours(-3),
            ClientIpAddress = "*************",
            UserAgent = "DongleKeyManager-Client/1.0",
            IsSuccess = false,
            ErrorMessage = "设备状态异常：已禁用",
            ExecutionTime = 38,
            Remarks = "验证失败：设备已禁用"
        };

        // 最近的一些查询日志
        yield return new DeviceLog
        {
            Id = logId++,
            DeviceId = 2000001,
            OperationType = LogOperationType.Query,
            OperationDescription = "查询设备信息",
            UserId = 1000002,
            Username = "admin",
            OperationTime = DateTime.Now.AddHours(-1),
            ClientIpAddress = "*************",
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IsSuccess = true,
            ExecutionTime = 25,
            Remarks = "管理员查询设备详情"
        };
    }
}
