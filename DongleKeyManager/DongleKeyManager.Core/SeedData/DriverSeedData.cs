using Common.Enums;

namespace Feng.IotGateway.Core.SeedData;

/// <summary>
/// </summary>
public class DriverSeedData : ISqlSugarEntitySeedData<Driver>
{
    public IEnumerable<Driver> HasData()
    {
        return new[]
        {
            #region 发那科

            new Driver
            {
                Id = DriverId.Fanuc,
                DriverName = "Fanuc",
                FileName = "Fanuc.dll",
                AssembleName = "Fanuc.Fanuc",
                DriveManufacturer = "发那科(Fanuc)"
            },
            new Driver
            {
                Id = DriverId.Fanuc18i,
                DriverName = "Fanuc18i",
                FileName = "Fanuc18i.dll",
                AssembleName = "Fanuc18i.Fanuc18i",
                DriveManufacturer = "发那科(Fanuc)"
            },
            new Driver
            {
                Id = DriverId.FanucMultiStation,
                DriverName = "FanucMultiStation",
                FileName = "FanucMultiStation.dll",
                AssembleName = "FanucMultiStation.FanucMultiStation",
                DriveManufacturer = "发那科(Fanuc)"
            },

            new Driver
            {
                Id = DriverId.FanucTtc,
                DriverName = "FanucTtc",
                FileName = "FanucTtc.dll",
                AssembleName = "FanucTtc.FanucTtc",
                DriveManufacturer = "发那科(Fanuc)"
            },

            #endregion 发那科

            #region 新代

            new Driver
            {
                Id = DriverId.SyntecV4,
                DriverName = "SyntecV4",
                FileName = "SyntecV4.dll",
                AssembleName = "SyntecV4.Syntec",
                DriveManufacturer = "新代(Syntec)",
            },
            new Driver
            {
                Id = DriverId.SyntecV2,
                DriverName = "SyntecV2",
                FileName = "SyntecV2.dll",
                AssembleName = "SyntecV2.Syntec",
                DriveManufacturer = "新代(Syntec)",
            },
            new Driver
            {
                Id = DriverId.SyntecV3,
                DriverName = "SyntecV3",
                FileName = "SyntecV3.dll",
                AssembleName = "SyntecV3.Syntec",
                DriveManufacturer = "新代(Syntec)",
            },
            new Driver
            {
                Id = DriverId.Syntec6AE,
                DriverName = "Syntec6AE",
                FileName = "Syntec6AE.dll",
                AssembleName = "Syntec6AE.Syntec",
                DriveManufacturer = "新代(Syntec)",
            },

            new Driver
            {
                Id = DriverId.Syntec6MA,
                DriverName = "Syntec6MA",
                FileName = "Syntec6MA.dll",
                AssembleName = "Syntec6MA.Syntec",
                DriveManufacturer = "新代(Syntec)",
            },

            #endregion 新代

            #region GSK

            new Driver
            {
                Id = DriverId.Gsk25im,
                DriverName = "Gsk25im",
                FileName = "Gsk25im.dll",
                AssembleName = "Gsk25im.Gsk25im",
                DriveManufacturer = "广州数控(GSK)",
            },
            new Driver
            {
                Id = DriverId.GskTcp,
                DriverName = "GskTcp",
                FileName = "GskTcp.dll",
                AssembleName = "GskTcp.GskTcp",
                DriveManufacturer = "广州数控(GSK)",
            },
            new Driver
            {
                Id = DriverId.GskUdp,
                DriverName = "GskUdp",
                FileName = "GskUdp.dll",
                AssembleName = "GskUdp.GskUdp",
                DriveManufacturer = "广州数控(GSK)",
            },

            #endregion GSK

            #region 凯恩蒂

            new Driver
            {
                Id = DriverId.Knd,
                DriverName = "Knd",
                FileName = "Knd.dll",
                AssembleName = "Knd.Knd",
                DriveManufacturer = "凯恩蒂(Knd)",
            },

            #endregion 凯恩蒂

            #region 科德

            new Driver
            {
                Id = DriverId.Kede,
                DriverName = "Kede",
                FileName = "Kede.dll",
                AssembleName = "Kede.Kede",
                DriveManufacturer = "科德(Kede)",
            },

            #endregion 科德

            #region ModBus

            new Driver
            {
                Id = DriverId.ModbusTcp,
                DriverName = "ModbusTcp",
                FileName = "ModbusTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusTcp.ModbusTcp",
                DriveManufacturer = "Modbus驱动",
            },
            new Driver
            {
                Id = DriverId.ModbusRtu,
                DriverName = "ModbusRtu",
                FileName = "ModbusRtu.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusRtu.ModbusRtu",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "Modbus驱动",
            },
            new Driver
            {
                Id = DriverId.ModbusAscii,
                DriverName = "ModbusAscii",
                FileName = "ModbusAscii.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusAscii.ModbusAscii",
                DriveManufacturer = "Modbus驱动",
            },

            new Driver
            {
                Id = DriverId.ModbusRtuOverTcp,
                DriverName = "ModbusRtuOverTcp",
                FileName = "ModbusRtuOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusRtuOverTcp.ModbusRtuOverTcp",
                DriveManufacturer = "Modbus驱动",
            },

            new Driver
            {
                Id = DriverId.ModbusAsciiOverTcp,
                DriverName = "ModbusAsciiOverTcp",
                FileName = "ModbusAsciiOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusAsciiOverTcp.ModbusAsciiOverTcp",
                DriveManufacturer = "Modbus驱动",
            },

            new Driver
            {
                Id = DriverId.ModbusUdpNet,
                DriverName = "ModbusUdpNet",
                FileName = "ModbusUdpNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "ModbusUdpNet.ModbusUdpNet",
                ConnectType = ConnectTypeEnum.Udp,
                DriveManufacturer = "Modbus驱动",
            },

            #endregion ModBus

            #region 台达(Delta)

            new Driver
            {
                Id = DriverId.DeltaTcp,
                DriverName = "DeltaTcp",
                FileName = "DeltaTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DeltaTcp.DeltaTcp",
                DriveManufacturer = "台达(Delta)",
            },
            new Driver
            {
                Id = DriverId.DeltaSerialAscii,
                DriverName = "DeltaSerialAscii",
                FileName = "DeltaSerialAscii.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DeltaSerialAscii.DeltaSerialAscii",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "台达(Delta)",
            },
            new Driver
            {
                Id = DriverId.DeltaSerialAsciiOverTcp,
                DriverName = "DeltaSerialAsciiOverTcp",
                FileName = "DeltaSerialAsciiOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DeltaSerialAsciiOverTcp.DeltaSerialAsciiOverTcp",
                DriveManufacturer = "台达(Delta)",
            },

            #endregion 台达

            #region 三菱(Melsec)

            new Driver
            {
                Id = DriverId.Mitsub,
                DriverName = "Mitsub",
                FileName = "Mitsub.dll",
                DriverType = DriverTypeEnum.Cnc,
                AssembleName = "Mitsub.Mitsub",
                DriveManufacturer = "三菱(Melsec)",
            },

            new Driver
            {
                Id = DriverId.McA1E,
                DriverName = "McA1E",
                FileName = "McA1E.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McA1E.McA1E",
                DriveManufacturer = "三菱(Melsec)",
            },
            new Driver
            {
                Id = DriverId.McFxLinks,
                DriverName = "McFxLinks",
                FileName = "McFxLinks.dll",
                DriverType = DriverTypeEnum.Plc,
                ConnectType = ConnectTypeEnum.SerialPort,
                AssembleName = "McFxLinks.McFxLinks",
                DriveManufacturer = "三菱(Melsec)",
            },
            new Driver
            {
                Id = DriverId.McQna3E,
                DriverName = "McQna3E",
                FileName = "McQna3E.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McQna3E.McQna3E",
                DriveManufacturer = "三菱(Melsec)",
            },
            new Driver
            {
                Id = DriverId.McUdp,
                DriverName = "McUdp",
                FileName = "McUdp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McUdp.McUdp",
                ConnectType = ConnectTypeEnum.Udp,
                DriveManufacturer = "三菱(Melsec)",
            },

            new Driver
            {
                Id = DriverId.MelsecA1EAscii,
                DriverName = "MelsecA1EAscii",
                FileName = "MelsecA1EAscii.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "MelsecA1EAscii.MelsecA1EAscii",
                DriveManufacturer = "三菱(Melsec)",
            },
            new Driver
            {
                Id = DriverId.McFxSerial,
                DriverName = "McFxSerial",
                FileName = "McFxSerial.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McFxSerial.McFxSerial",
                DriveManufacturer = "三菱(Melsec)",
            },
            #endregion Melsec

            #region 罗克韦尔(AllenBradley)

            new Driver
            {
                Id = DriverId.AllenBradleyCip,
                DriverName = "AllenBradleyCip",
                FileName = "AllenBradleyCip.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "AllenBradleyCip.AllenBradleyCip",
                DriveManufacturer = "罗克韦尔(AllenBradley)",
            },
            new Driver
            {
                Id = DriverId.AllenBradleyPccc,
                DriverName = "AllenBradleyPccc",
                FileName = "AllenBradleyPccc.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "AllenBradleyPccc.AllenBradleyPccc",
                DriveManufacturer = "罗克韦尔(AllenBradley)",
            },

            #endregion 霍尼韦尔

            #region 欧姆龙(Omron)

            new Driver
            {
                Id = DriverId.FinsNet,
                DriverName = "FinsNet",
                FileName = "FinsNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "FinsNet.FinsNet",
                DriveManufacturer = "欧姆龙(Omron)",
            },
            new Driver
            {
                Id = DriverId.FinsUdp,
                DriverName = "FinsUdp",
                FileName = "FinsUdp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "FinsUdp.FinsUdp",
                ConnectType = ConnectTypeEnum.Udp,
                DriveManufacturer = "欧姆龙(Omron)",
            },
            new Driver
            {
                Id = DriverId.CipNet,
                DriverName = "CipNet",
                FileName = "CipNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "CipNet.OmronCipNet",
                DriveManufacturer = "欧姆龙(Omron)",
            },

            new Driver
            {
                Id = DriverId.HostLink,
                DriverName = "HostLink",
                FileName = "HostLink.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "HostLink.HostLink",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "欧姆龙(Omron)",
            },

            new Driver
            {
                Id = DriverId.HostLinkOverTcp,
                DriverName = "HostLinkOverTcp",
                FileName = "HostLinkOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "HostLinkOverTcp.HostLinkOverTcp",
                DriveManufacturer = "欧姆龙(Omron)",
            },

            #endregion 欧姆龙

            #region 松下(Panasonic)

            new Driver
            {
                Id = DriverId.MewtocolOverTcp,
                DriverName = "MewtocolOverTcp",
                FileName = "MewtocolOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "MewtocolOverTcp.MewtocolOverTcp",
                DriveManufacturer = "松下(Panasonic)",
            },

            #endregion 松下

            #region 西门子(Siemens)

            new Driver
            {
                Id = DriverId.Siemens,
                DriverName = "Siemens",
                FileName = "Siemens.dll",
                DriverType = DriverTypeEnum.Cnc,
                AssembleName = "Siemens.Siemens",
                DriveManufacturer = "西门子(Siemens)",
            },

            new Driver
            {
                Id = DriverId.SiemensS7,
                DriverName = "SiemensS7",
                FileName = "SiemensS7.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "SiemensS7.SiemensS7",
                DriveManufacturer = "西门子(Siemens)",
            },
            new Driver
            {
                Id = DriverId.SiemensPPI,
                DriverName = "SiemensPPI",
                FileName = "SiemensPPI.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "SiemensPPI.SiemensPPI",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "西门子(Siemens)",
            },

            #endregion 西门子

            #region 自由协议

            new Driver
            {
                Id = DriverId.OpcUaClient,
                DriverName = "OpcUaClient",
                FileName = "OpcUaClient.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "OpcUaClient.OpcUa",
                DriveManufacturer = "自由协议(Free)",
            },

            new Driver
            {
                Id = DriverId.MtConnectClient,
                DriverName = "MtConnectClient",
                FileName = "MtConnect.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "MtConnect.MtConnectClient",
                DriveManufacturer = "自由协议(Free)",
            },

            new Driver
            {
                Id = DriverId.TcpClient,
                DriverName = "TcpClient",
                FileName = "TcpClient.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "TcpClient.TcpClient",
                DriveManufacturer = "自由协议(Free)",
            },

            new Driver
            {
                Id = DriverId.SerialClient,
                DriverName = "SerialClient",
                FileName = "SerialClient.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "SerialClient.SerialClient",
                DriveManufacturer = "自由协议(Free)",
            },
            

            #endregion 自由协议

            #region 信捷(XinJE)

            new Driver
            {
                Id = DriverId.XinJETcpNet,
                DriverName = "XinJE TcpNet(ModBus)",
                FileName = "XinJETcpNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "XinJETcpNet.XinJETcpNet",
                DriveManufacturer = "信捷(XinJE)",
            },

            new Driver
            {
                Id = DriverId.XinJESerialOverTcp,
                DriverName = "XinJESerialOverTcp",
                FileName = "XinJESerialOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "XinJESerialOverTcp.XinJESerialOverTcp",
                DriveManufacturer = "信捷(XinJE)",
            },

            new Driver
            {
                Id = DriverId.XinJESerial,
                DriverName = "XinJESerial",
                FileName = "XinJESerial.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "XinJESerial.XinJESerial",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "信捷(XinJE)",
            },

            new Driver
            {
                Id = DriverId.XinJeTcpNet,
                DriverName = "XinJE TcpNet(专用)",
                FileName = "XinJEInternalNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "XinJEInternalNet.XinJEInternalNet",
                DriveManufacturer = "信捷(XinJE)",
            },

            #endregion 信捷

            #region 永宏(Fatek)

            new Driver
            {
                Id = DriverId.ProgramOverTcp,
                DriverName = "Program OverTcp",
                FileName = "FatekProgramOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "FatekProgramOverTcp.FatekProgramOverTcp",
                DriveManufacturer = "永宏(Fatek)",
            },

            #endregion 永宏

            #region 电力协议

            new Driver
            {
                Id = DriverId.Dlt645,
                DriverName = "Dlt645",
                FileName = "DLT645.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DLT645.Dlt645",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "电力协议(Dlt)",
            },

            new Driver
            {
                Id = DriverId.Dlt645OverTcp,
                DriverName = "Dlt645OverTcp",
                FileName = "DLT645OverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DLT645OverTcp.Dlt645OverTcp",
                DriveManufacturer = "电力协议(Dlt)",
            },

            new Driver
            {
                Id = DriverId.Dlt698,
                DriverName = "Dlt698",
                FileName = "DLT698.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DLT698.Dlt698",
                ConnectType = ConnectTypeEnum.SerialPort,
                DriveManufacturer = "电力协议(Dlt)",
            },

            new Driver
            {
                Id = DriverId.Dlt698OverTcp,
                DriverName = "Dlt698OverTcp",
                FileName = "DLT698OverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DLT698OverTcp.Dlt698OverTcp",
                DriveManufacturer = "电力协议(Dlt)",
            },

            new Driver
            {
                Id = DriverId.Dlt698TcpNet,
                DriverName = "Dlt698TcpNet",
                FileName = "DLT698TcpNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "DLT698TcpNet.Dlt698TcpNet",
                DriveManufacturer = "电力协议(Dlt)",
            },

            #endregion

            #region 汇川(Inovance)

            new Driver
            {
                Id = DriverId.InovanceTcp,
                DriverName = "InovanceTcp",
                FileName = "InovanceTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "InovanceTcp.InovanceTcp",
                DriveManufacturer = "汇川(Inovance)",
            },

            #endregion

            #region 马扎克(Mazak)

            new Driver
            {
                Id = DriverId.MazakMatrix,
                DriverName = "MazakMatrix",
                FileName = "MazakMatrix.dll",
                AssembleName = "MazakMatrix.MazakMatrix",
                DriveManufacturer = "马扎克(Mazak)",
            },

            new Driver
            {
                Id = DriverId.MazakSmooth,
                DriverName = "MazakSmooth",
                FileName = "MazakSmooth.dll",
                AssembleName = "MazakSmooth.MazakSmooth",
                DriveManufacturer = "马扎克(Mazak)",
            },

            new Driver
            {
                Id = DriverId.MazakSmart,
                DriverName = "MazakSmart",
                FileName = "MazakSmart.dll",
                AssembleName = "MazakSmart.MazakSmart",
                DriveManufacturer = "马扎克(Mazak)",
            },

            #endregion

            #region Snmp

            new Driver
            {
                Id = DriverId.Snmp,
                DriverName = "Snmp",
                FileName = "Snmp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "Snmp.Snmp",
                ConnectType = ConnectTypeEnum.Udp,
                DriveManufacturer = "自由协议(Free)",
            },

            #endregion Snmp

            #region 海德汉(Heidenhain)

            new Driver
            {
                Id = DriverId.Heidenhain,
                DriverName = "Heidenhain",
                FileName = "Heidenhain.dll",
                AssembleName = "Heidenhain.Heidenhain",
                DriveManufacturer = "海德汉(Heidenhain)",
            },

            #endregion 海德汉(Heidenhain)

            #region 宝元(Lnc)

            new Driver
            {
                Id = DriverId.LncTcp,
                DriverName = "LncTcp",
                FileName = "LncTcp.dll",
                AssembleName = "LncTcp.LncTcp",
                DriveManufacturer = "宝元(Lnc)",
            },

            #endregion 宝元(Lnc)

            #region MQTT Server

            new Driver
            {
                Id = DriverId.MqttServer,
                DriverName = "MqttServer",
                FileName = "MqttServer.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "MqttServer.MqttServer",
                DriveManufacturer = "自由协议(Free)",
            },

            new Driver
            {
                Id = DriverId.MqttServerEx,
                DriverName = "MqttServerEx",
                FileName = "MqttServerEx.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "MqttServerEx.MqttServerEx",
                DriveManufacturer = "自由协议(Free)",
            },

            #endregion MQTT Server

            #region 法格

            new Driver
            {
                Id = DriverId.FagorNet,
                DriverName = "FagorNet",
                FileName = "FagorNet.dll",
                AssembleName = "FagorNet.FagorNet",
                DriveManufacturer = "法格(Fagor)",
            },

            #endregion 法格

            #region 华中

            new Driver
            {
                Id = DriverId.Hnc,
                DriverName = "Hnc",
                FileName = "Hnc.dll",
                AssembleName = "Hnc.Hnc",
                DriveManufacturer = "华中(Hnc)",
            },

            #endregion 华中

            #region OpcDa

            new Driver
            {
                Id = DriverId.OpcDa,
                DriverName = "OpcDaClient",
                FileName = "OpcDaClient.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "OpcDaClient.OpcDaClient",
                DriveManufacturer = "OpcDa",
            },

            #endregion OpcDa

            #region 虚拟协议

            new Driver
            {
                Id = DriverId.VirtualProtocol,
                DriverName = "VirtualProtocol",
                FileName = "VirtualProtocol.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "VirtualProtocol.VirtualProtocol",
                DriveManufacturer = "虚拟协议",
            },

            #endregion 虚拟协议

            #region 注塑机

            new Driver
            {
                Id = DriverId.PORCHESON_Ps660Bm,
                DriverName = "PORCHESON_Ps660Bm",
                FileName = "PORCHESON_Ps660Bm.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "PORCHESON_Ps660Bm.PORCHESON_Ps660Bm",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.TechMationAk,
                DriverName = "TechMationAk",
                FileName = "TechMationAk.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "TechMationAk.TechMationAk",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.PORCHESON_Ps660Am,
                DriverName = "PORCHESON_Ps660Am",
                FileName = "PORCHESON_Ps660Am.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "PORCHESON_Ps660Am.PORCHESON_Ps660Am",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.KeQiangT6H3,
                DriverName = "KeQiang_T6H3",
                FileName = "KeQiang_T6H3.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "KeQiang_T6H3.KeQiangT6H3",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.KeQiangT6F3,
                DriverName = "KeQiang_T6F3",
                FileName = "KeQiang_T6F3.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "KeQiang_T6F3.KeQiangT6F3",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.TechMation5521,
                DriverName = "TechMation5521",
                FileName = "TechMation5521.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "TechMation5521.TechMation5521",
                DriveManufacturer = "注塑机",
            },

            new Driver
            {
                Id = DriverId.HongXunAkDouble,
                DriverName = "HongXunAkDouble",
                FileName = "HongXunAkDouble.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "HongXunAkDouble.HongXunAkDouble",
                DriveManufacturer = "注塑机",
            },

            #endregion 注塑机

            #region 卷绕机

            new Driver
            {
                Id = DriverId.WarpingMachine,
                DriverName = "WarpingMachine",
                FileName = "WarpingMachine.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "WarpingMachine.WarpingMachine",
                DriveManufacturer = "卷绕机",
            },

            #endregion 卷绕机

            #region 基恩士(Keyence)

            new Driver
            {
                Id = DriverId.McNet,
                DriverName = "McNet",
                FileName = "McNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McNet.McNet",
                DriveManufacturer = "基恩士(Keyence)",
            },

            new Driver
            {
                Id = DriverId.McAsciiNet,
                DriverName = "McAsciiNet",
                FileName = "McAsciiNet.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "McAsciiNet.McAsciiNet",
                DriveManufacturer = "基恩士(Keyence)",
            },

            new Driver
            {
                Id = DriverId.NanoSerial,
                DriverName = "NanoSerial",
                FileName = "NanoSerial.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "NanoSerial.NanoSerial",
                DriveManufacturer = "基恩士(Keyence)",
                ConnectType = ConnectTypeEnum.SerialPort
            },

            new Driver
            {
                Id = DriverId.NanoSerialOverTcp,
                DriverName = "NanoSerialOverTcp",
                FileName = "NanoSerialOverTcp.dll",
                DriverType = DriverTypeEnum.Plc,
                AssembleName = "NanoSerialOverTcp.NanoSerialOverTcp",
                DriveManufacturer = "基恩士(Keyence)",
            },

            #endregion 基恩士(Keyence)

            #region 兄弟(Brother)

            new Driver
            {
                Id = DriverId.Brother,
                DriverName = "Brother",
                FileName = "Brother.dll",
                DriverType = DriverTypeEnum.Cnc,
                AssembleName = "Brother.Brother",
                DriveManufacturer = "兄弟(Brother)",
            },

            #endregion 兄弟(Brother)
        };
    }
}