namespace Feng.IotGateway.Core.SeedData;

public class SysDictTypeSeedData : ISqlSugarEntitySeedData<SysDictType>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysDictType> HasData()
    {
        return new[]
        {
            new SysDictType {Id = 142307070905001, Name = "SQL变量", Code = "driver_SqlVar", Sort = 100, Remark = "SQL插入变量", Enable = true, System = true},
            // new SysDictType {Id = 142307070906000, Name = "数据类型", Code = "driver_datatype", Sort = 100, Remark = "读取数据类型", Enable = true, SystemServices = true},
            new SysDictType {Id = 142307070906001, Name = "转发数据类型", Code = "tranPond_datatype", Sort = 100, Remark = "转发数据类型", Enable = true, System = true},
            new SysDictType {Id = 1423070709139000, Name = "ModbusTcp", Code = "driver_modbusTcp", Sort = 100, Remark = "Modbus", Enable = true, System = true},
            new SysDictType {Id = 1523070709139000, Name = "ModbusRtu", Code = "driverDriver", Sort = 100, Remark = "Modbus", Enable = true, System = true},
            new SysDictType {Id = 1523070709139015, Name = "OpcUaClient", Code = "driver_OpcUaClient", Sort = 100, Remark = "OpcUa", Enable = true, System = true},
            new SysDictType {Id = 1523070709139013, Name = "SiemensS7", Code = "driver_SiemensS7", Sort = 100, Remark = "Siemens", Enable = true, System = true},
            new SysDictType {Id = 1523070709139014, Name = "SiemensPPI", Code = "driver_SiemensPPI", Sort = 100, Remark = "Siemens", Enable = true, System = true},
            new SysDictType {Id = 1523070709139010, Name = "FinsUdp", Code = "driver_FinsUdp", Sort = 100, Remark = "欧姆龙", Enable = true, System = true},
            new SysDictType {Id = 1523070709139009, Name = "FinsNet", Code = "driver_FinsNet", Sort = 100, Remark = "欧姆龙", Enable = true, System = true},
            new SysDictType {Id = 1523070709139008, Name = "AllenBradleyCip", Code = "driver_AllenBradleyCip", Sort = 100, Remark = "霍尼韦尔", Enable = true, System = true},
            new SysDictType {Id = 1523070709139003, Name = "ModbusAscii", Code = "driver_ModbusAscii", Sort = 100, Remark = "Modbus", Enable = true, System = true},
            new SysDictType {Id = 1523070709139011, Name = "OmronCipNet", Code = "driver_OmronCipNet", Sort = 100, Remark = "欧姆龙", Enable = true, System = true},
            new SysDictType {Id = 1523070709139007, Name = "McUdp", Code = "driver_McUdp", Sort = 100, Remark = "三菱", Enable = true, System = true},
            new SysDictType {Id = 1523070709139006, Name = "McQna3E", Code = "driver_McQna3E", Sort = 100, Remark = "三菱", Enable = true, System = true},
            new SysDictType {Id = 1523070709139005, Name = "McFxLinks", Code = "driver_McFxLinks", Sort = 100, Remark = "三菱", Enable = true, System = true},
            new SysDictType {Id = 1523070709139004, Name = "McA1E", Code = "driver_McA1E", Sort = 100, Remark = "三菱", Enable = true, System = true},
            new SysDictType {Id = 1423070709139001, Name = "Fanuc", Code = "driver_Fanuc", Sort = 100, Remark = "法拉克", Enable = true, System = true},
            new SysDictType {Id = 1523070709139002, Name = "DeltaSerialAscii", Code = "driver_DeltaSerialAscii", Sort = 100, Remark = "台达", Enable = true, System = true},
            new SysDictType {Id = 1523070709139001, Name = "DeltaTcp", Code = "driver_DeltaTcp", Sort = 100, Remark = "台达", Enable = true, System = true},
            new SysDictType {Id = 1523070709139012, Name = "MewtocolOverTcp", Code = "driver_MewtocolOverTcp", Sort = 100, Remark = "MewtocolOverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139016, Name = "MtConnectClient", Code = "driver_MtConnectClient", Sort = 100, Remark = "MtConnectClient驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139002, Name = "Fanuc18i", Code = "driver_Fanuc18i", Sort = 100, Remark = "Fanuc18i驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139003, Name = "FanucMultiStation", Code = "driver_FanucMultiStation", Sort = 100, Remark = "FanucMultiStation驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139004, Name = "Simens", Code = "driver_Simens", Sort = 100, Remark = "Simens驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139005, Name = "SyntecV1", Code = "driver_SyntecV1", Sort = 100, Remark = "SyntecV1驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139006, Name = "SyntecV2", Code = "driver_SyntecV2", Sort = 100, Remark = "SyntecV2驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139007, Name = "SyntecV3", Code = "driver_SyntecV3", Sort = 100, Remark = "SyntecV3驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139008, Name = "Mitsub", Code = "driver_Mitsub", Sort = 100, Remark = "Mitsub驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139009, Name = "Gsk25im", Code = "driver_Gsk25im", Sort = 100, Remark = "Gsk25im驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139010, Name = "GskTcp", Code = "driver_GskTcp", Sort = 100, Remark = "GskTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139011, Name = "GskUdp", Code = "driver_GskUdp", Sort = 100, Remark = "GskUdp驱动", Enable = true, System = true},
            new SysDictType {Id = 1423070709139012, Name = "Knd", Code = "driver_Knd", Sort = 100, Remark = "Knd驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139018, Name = "Syntec6AE", Code = "driver_Syntec6AE", Sort = 100, Remark = "Syntec6AE驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139019, Name = "ModbusRtuOverTcp", Code = "driver_ModbusRtuOverTcp", Sort = 100, Remark = "ModbusRtuOverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139020, Name = "ModbusUdpNet", Code = "driver_ModbusUdpNet", Sort = 100, Remark = "ModbusUdpNet驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139021, Name = "ModbusAsciiOverTcp", Code = "driver_ModbusAsciiOverTcp", Sort = 100, Remark = "ModbusAsciiOverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139022, Name = "XinJE TcpNet(ModBus)", Code = "driver_XinJE TcpNet(ModBus)", Sort = 100, Remark = "XinJE TcpNet(ModBus)驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139023, Name = "XinJESerialOverTcp", Code = "driver_XinJESerialOverTcp", Sort = 100, Remark = "XinJESerialOverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139024, Name = "XinJESerial", Code = "driver_XinJESerial", Sort = 100, Remark = "XinJESerial驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139025, Name = "XinJE TcpNet(专用)", Code = "driver_XinJE TcpNet(专用)", Sort = 100, Remark = "XinJE TcpNet(专用)驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139026, Name = "TcpClient", Code = "driver_TcpClient", Sort = 100, Remark = "TcpClient驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139027, Name = "Program OverTcp", Code = "driver_Program OverTcp", Sort = 100, Remark = "Program OverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139028, Name = "Dlt645", Code = "driver_Dlt645", Sort = 100, Remark = "Dlt645驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139029, Name = "Dlt645OverTcp", Code = "driver_Dlt645OverTcp", Sort = 100, Remark = "Dlt645OverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139030, Name = "Dlt698", Code = "driver_Dlt698", Sort = 100, Remark = "Dlt698驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139031, Name = "Dlt698OverTcp", Code = "driver_Dlt698OverTcp", Sort = 100, Remark = "Dlt698OverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139032, Name = "Dlt698TcpNet", Code = "driver_Dlt698TcpNet", Sort = 100, Remark = "Dlt698TcpNet驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139033, Name = "InovanceTcp", Code = "driver_InovanceTcp", Sort = 100, Remark = "InovanceTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139034, Name = "HostLink", Code = "driver_HostLink", Sort = 100, Remark = "HostLink驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139035, Name = "HostLinkOverTcp", Code = "driver_HostLinkOverTcp", Sort = 100, Remark = "HostLinkOverTcp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139036, Name = "MazakMatrix", Code = "driver_MazakMatrix", Sort = 100, Remark = "MazakMatrix驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139037, Name = "MazakSmooth", Code = "driver_MazakSmooth", Sort = 100, Remark = "MazakSmooth驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139038, Name = "Snmp", Code = "driver_Snmp", Sort = 100, Remark = "Snmp驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139039, Name = "Heidenhain", Code = "driver_Heidenhain", Sort = 100, Remark = "Heidenhain驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139040, Name = "FanucTtc", Code = "driver_FanucTtc", Sort = 100, Remark = "FanucTtc驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139041, Name = "MelsecA1EAscii", Code = "driver_MelsecA1EAscii", Sort = 100, Remark = "MelsecA1EAscii驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139042, Name = "LncTcp", Code = "driver_LncTcp", Sort = 100, Remark = "LncTcp驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.MqttServer, Name = "MqttServer", Code = "driver_MqttServer", Sort = 100, Remark = "MqttServer驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139044, Name = "MazakSmart", Code = "driver_MazakSmart", Sort = 100, Remark = "MazakSmart驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139045, Name = "Syntec6MA", Code = "driver_Syntec6MA", Sort = 100, Remark = "Syntec6MA驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139046, Name = "FagorNet", Code = "driver_FagorNet", Sort = 100, Remark = "FagorNet驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.Hnc, Name = "Hnc", Code = "driver_Hnc", Sort = 100, Remark = "Hnc驱动", Enable = true, System = true},
            new SysDictType {Id = 1523070709139048, Name = "OpcDa", Code = "driver_OpcDa", Sort = 100, Remark = "OpcDa驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.VirtualProtocol, Name = "VirtualProtocol", Code = "driver_VirtualProtocol", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.AllenBradleyPccc, Name = "AllenBradleyPccc", Code = "driver_AllenBradleyPccc", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.PORCHESON_Ps660Bm, Name = "PORCHESON_Ps660Bm", Code = "driver_PORCHESON_Ps660Bm", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.TechMationAk, Name = "TechMationAk", Code = "driver_TechMationAk", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.DeltaSerialAsciiOverTcp, Name = "DeltaSerialAsciiOverTcp", Code = "driver_DeltaSerialAsciiOverTcp", Sort = 100, Remark = "台达", Enable = true, System = true},
            new SysDictType {Id = DriverId.PORCHESON_Ps660Am, Name = "PORCHESON_Ps660Am", Code = "driver_PORCHESON_Ps660Am", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.McNet, Name = "McNet", Code = "driver_McNet", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.McAsciiNet, Name = "McAsciiNet", Code = "driver_McAsciiNet", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.NanoSerial, Name = "NanoSerial", Code = "driver_NanoSerial", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.NanoSerialOverTcp, Name = "NanoSerialOverTcp", Code = "driver_NanoSerialOverTcp", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.SerialClient, Name = "SerialClient", Code = "driver_SerialClient", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.KeQiangT6H3, Name = "KeQiang_T6H3", Code = "driver_KeQiang_T6H3", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.KeQiangT6F3, Name = "KeQiang_T6F3", Code = "driver_KeQiang_T6F3", Sort = 100, Remark = "", Enable = true, System = true},
            new SysDictType {Id = DriverId.MqttServerEx, Name = "MqttServerEx", Code = "driver_MqttServerEx", Sort = 100, Remark = "MqttServerEx驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.WarpingMachine, Name = "WarpingMachine", Code = "driver_WarpingMachine", Sort = 100, Remark = "卷绕机驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.TechMation5521, Name = "TechMation5521", Code = "driver_TechMation5521", Sort = 100, Remark = "注塑机驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.Brother, Name = "Brother", Code = "driver_Brother", Sort = 100, Remark = "兄弟驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.HongXunAkDouble, Name = "HongXunAkDouble", Code = "driver_HongXunAkDouble", Sort = 100, Remark = "弘讯驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.McFxSerial, Name = "McFxSerial", Code = "driver_McFxSerial", Sort = 100, Remark = "三菱(Melsec)驱动", Enable = true, System = true},
            new SysDictType {Id = DriverId.Kede, Name = "Kede", Code = "driver_Kede", Sort = 100, Remark = "科德(Kede)驱动", Enable = true, System = true},
        };
    }
}