using DongleKeyManager.Core.Entities;
using System.Security.Cryptography;
using System.Text;

namespace DongleKeyManager.Core.SeedData;

/// <summary>
/// 种子数据服务实现
/// </summary>
public class SeedDataService : ISeedDataService
{
    /// <summary>
    /// 初始化所有种子数据
    /// </summary>
    public async Task InitializeAllSeedDataAsync()
    {
        Console.WriteLine("开始初始化种子数据...");

        // 初始化用户数据
        await InitializeUserSeedDataAsync();

        // 初始化设备数据
        await InitializeDeviceSeedDataAsync();

        Console.WriteLine("种子数据初始化完成");
    }

    /// <summary>
    /// 初始化用户种子数据
    /// </summary>
    public async Task InitializeUserSeedDataAsync()
    {
        // 检查是否已存在用户数据
        var existingUserCount = await DbContext.Instance.Queryable<User>()
            .Where(x => !x.IsDeleted)
            .CountAsync();

        if (existingUserCount > 0)
        {
            Console.WriteLine("用户数据已存在，跳过用户种子数据初始化");
            return;
        }

        var users = GetUserSeedData();
        
        foreach (var user in users)
        {
            await DbContext.Instance.Insertable(user).ExecuteCommandAsync();
        }

        Console.WriteLine($"用户种子数据初始化完成，共创建 {users.Count} 个用户");
    }

    /// <summary>
    /// 初始化设备种子数据
    /// </summary>
    public async Task InitializeDeviceSeedDataAsync()
    {
        // 检查是否已存在设备数据
        var existingDeviceCount = await DbContext.Instance.Queryable<DongleDevice>()
            .Where(x => !x.IsDeleted)
            .CountAsync();

        if (existingDeviceCount > 0)
        {
            Console.WriteLine("设备数据已存在，跳过设备种子数据初始化");
            return;
        }

        var devices = GetDeviceSeedData();
        
        foreach (var device in devices)
        {
            await DbContext.Instance.Insertable(device).ExecuteCommandAsync();
        }

        Console.WriteLine($"设备种子数据初始化完成，共创建 {devices.Count} 个设备");
    }

    /// <summary>
    /// 清空所有数据
    /// </summary>
    public async Task ClearAllDataAsync()
    {
        Console.WriteLine("开始清空所有数据...");

        // 清空设备日志
        await DbContext.Instance.Deleteable<DeviceLog>().ExecuteCommandAsync();
        
        // 清空设备数据
        await DbContext.Instance.Deleteable<DongleDevice>().ExecuteCommandAsync();
        
        // 清空用户数据
        await DbContext.Instance.Deleteable<User>().ExecuteCommandAsync();

        Console.WriteLine("所有数据清空完成");
    }

    /// <summary>
    /// 重置为初始状态
    /// </summary>
    public async Task ResetToInitialStateAsync()
    {
        await ClearAllDataAsync();
        await InitializeAllSeedDataAsync();
        Console.WriteLine("系统已重置为初始状态");
    }

    /// <summary>
    /// 获取用户种子数据
    /// </summary>
    private List<User> GetUserSeedData()
    {
        var users = new List<User>();

        // 超级管理员
        var superAdminSalt = GenerateSalt();
        var superAdminPasswordHash = GeneratePasswordHash("admin123", superAdminSalt);
        users.Add(new User
        {
            Id = 1000001,
            Username = "superadmin",
            PasswordHash = superAdminPasswordHash,
            Salt = superAdminSalt,
            Role = UserRole.SuperAdmin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认超级管理员账户"
        });

        // 管理员
        var adminSalt = GenerateSalt();
        var adminPasswordHash = GeneratePasswordHash("admin123", adminSalt);
        users.Add(new User
        {
            Id = 1000002,
            Username = "admin",
            PasswordHash = adminPasswordHash,
            Salt = adminSalt,
            Role = UserRole.Admin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认管理员账户"
        });

        // 测试用户
        var testUserSalt = GenerateSalt();
        var testUserPasswordHash = GeneratePasswordHash("user123", testUserSalt);
        users.Add(new User
        {
            Id = 1000003,
            Username = "testuser",
            PasswordHash = testUserPasswordHash,
            Salt = testUserSalt,
            Role = UserRole.User,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统测试用户账户"
        });

        // 演示用户
        var demoUserSalt = GenerateSalt();
        var demoUserPasswordHash = GeneratePasswordHash("demo123", demoUserSalt);
        users.Add(new User
        {
            Id = 1000004,
            Username = "demo",
            PasswordHash = demoUserPasswordHash,
            Salt = demoUserSalt,
            Role = UserRole.User,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统演示用户账户"
        });

        return users;
    }

    /// <summary>
    /// 获取设备种子数据
    /// </summary>
    private List<DongleDevice> GetDeviceSeedData()
    {
        var devices = new List<DongleDevice>();
        var baseTime = DateTime.Now.AddDays(-30); // 30天前创建

        // 演示设备1 - 已激活
        var device1HardwareId = "HW-DEMO-001-ABCD1234";
        devices.Add(new DongleDevice
        {
            Id = 2000001,
            SerialNumber = "SN-DEMO-001",
            HardwareUniqueId = device1HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device1HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备001",
            Description = "用于系统演示的看门狗设备",
            CreatedTime = baseTime,
            ActivatedTime = baseTime.AddDays(1),
            ExpiryTime = DateTime.Now.AddYears(1),
            LastUsedTime = DateTime.Now.AddHours(-2),
            UsageCount = 156,
            CreatedUserId = 1000002, // admin创建
            Remarks = "演示用设备，已激活状态"
        });

        // 演示设备2 - 未激活
        var device2HardwareId = "HW-DEMO-002-EFGH5678";
        devices.Add(new DongleDevice
        {
            Id = 2000002,
            SerialNumber = "SN-DEMO-002",
            HardwareUniqueId = device2HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device2HardwareId),
            Status = DeviceStatus.Inactive,
            DeviceName = "演示设备002",
            Description = "待激活的看门狗设备",
            CreatedTime = baseTime.AddDays(5),
            ExpiryTime = DateTime.Now.AddYears(1),
            UsageCount = 0,
            CreatedUserId = 1000002, // admin创建
            Remarks = "演示用设备，未激活状态"
        });

        // 演示设备3 - 已禁用
        var device3HardwareId = "HW-DEMO-003-IJKL9012";
        devices.Add(new DongleDevice
        {
            Id = 2000003,
            SerialNumber = "SN-DEMO-003",
            HardwareUniqueId = device3HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device3HardwareId),
            Status = DeviceStatus.Disabled,
            DeviceName = "演示设备003",
            Description = "已禁用的看门狗设备",
            CreatedTime = baseTime.AddDays(10),
            ActivatedTime = baseTime.AddDays(11),
            ExpiryTime = DateTime.Now.AddYears(1),
            LastUsedTime = DateTime.Now.AddDays(-5),
            UsageCount = 89,
            CreatedUserId = 1000002, // admin创建
            UpdatedUserId = 1000001, // superadmin禁用
            UpdatedTime = DateTime.Now.AddDays(-3),
            Remarks = "演示用设备，已禁用状态"
        });

        // 演示设备4 - 即将过期
        var device4HardwareId = "HW-DEMO-004-MNOP3456";
        devices.Add(new DongleDevice
        {
            Id = 2000004,
            SerialNumber = "SN-DEMO-004",
            HardwareUniqueId = device4HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device4HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备004",
            Description = "即将过期的看门狗设备",
            CreatedTime = baseTime.AddDays(15),
            ActivatedTime = baseTime.AddDays(16),
            ExpiryTime = DateTime.Now.AddDays(7), // 7天后过期
            LastUsedTime = DateTime.Now.AddMinutes(-30),
            UsageCount = 234,
            CreatedUserId = 1000003, // testuser创建
            Remarks = "演示用设备，即将过期"
        });

        // 演示设备5 - 高使用频率
        var device5HardwareId = "HW-DEMO-005-QRST7890";
        devices.Add(new DongleDevice
        {
            Id = 2000005,
            SerialNumber = "SN-DEMO-005",
            HardwareUniqueId = device5HardwareId,
            EncryptedString = GenerateDeviceEncryptedString(device5HardwareId),
            Status = DeviceStatus.Active,
            DeviceName = "演示设备005",
            Description = "高频使用的看门狗设备",
            CreatedTime = baseTime.AddDays(20),
            ActivatedTime = baseTime.AddDays(21),
            ExpiryTime = DateTime.Now.AddYears(2),
            LastUsedTime = DateTime.Now.AddMinutes(-5),
            UsageCount = 1567,
            CreatedUserId = 1000004, // demo创建
            Remarks = "演示用设备，高频使用"
        });

        return devices;
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    private string GenerateSalt(int length = 16)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var salt = new char[length];

        for (int i = 0; i < length; i++)
        {
            salt[i] = chars[random.Next(chars.Length)];
        }

        return new string(salt);
    }

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    private string GeneratePasswordHash(string password, string salt)
    {
        var saltedPassword = password + salt;
        using var sha256 = SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(saltedPassword);
        var hashBytes = sha256.ComputeHash(inputBytes);

        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 基于硬件唯一标识号生成加密字符串
    /// </summary>
    private string GenerateDeviceEncryptedString(string hardwareUniqueId)
    {
        var saltedInput = hardwareUniqueId + "DongleKeyManager2024";
        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(saltedInput);
        var hashBytes = md5.ComputeHash(inputBytes);

        var sb = new StringBuilder();
        foreach (var b in hashBytes)
        {
            sb.Append(b.ToString("x2"));
        }

        return sb.ToString();
    }
}
