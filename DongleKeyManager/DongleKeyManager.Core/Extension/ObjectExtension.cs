using System.Collections;
using System.Data;
using System.IO.Compression;
using System.Reflection;
using System.Text;
using System.Web;
using Furion.DependencyInjection;
using Furion.EventBus;
using Furion.JsonSerialization;
using Microsoft.AspNetCore.Http;
using SqlSugar;

namespace Feng.IotGateway.Core.Extension;

/// <summary>
///     对象拓展类
/// </summary>
[SuppressSniffer]
public static class ObjectExtension
{
    /// <summary>
    ///     判断类型是否实现某个泛型
    /// </summary>
    /// <param name="type">类型</param>
    /// <param name="generic">泛型类型</param>
    /// <returns>bool</returns>
    public static bool HasImplementedRawGeneric(this Type type, Type generic)
    {
        // 检查接口类型
        var isTheRawGenericType = type.GetInterfaces().Any(IsTheRawGenericType);
        if (isTheRawGenericType) return true;

        // 检查类型
        while (type != null && type != typeof(object))
        {
            isTheRawGenericType = IsTheRawGenericType(type);
            if (isTheRawGenericType) return true;
            type = type.BaseType;
        }

        return false;

        // 判断逻辑
        bool IsTheRawGenericType(Type type)
        {
            return generic == (type.IsGenericType ? type.GetGenericTypeDefinition() : type);
        }
    }

    /// <summary>
    ///     将字典转化为QueryString格式
    /// </summary>
    /// <param name="dict"></param>
    /// <param name="urlEncode"></param>
    /// <returns></returns>
    public static string ToQueryString(this Dictionary<string, string> dict, bool urlEncode = true)
    {
        return string.Join("&", dict.Select(p => $"{(urlEncode ? p.Key?.UrlEncode() : "")}={(urlEncode ? p.Value?.UrlEncode() : "")}"));
    }

    /// <summary>
    ///     将字符串URL编码
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static string UrlEncode(this string str)
    {
        return string.IsNullOrEmpty(str) ? "" : HttpUtility.UrlEncode(str, Encoding.UTF8);
    }

    /// <summary>
    ///     List转DataTable
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list"></param>
    /// <returns></returns>
    public static DataTable ToDataTable<T>(this List<T> list)
    {
        DataTable result = new();
        if (list.Count > 0)
        {
            result.TableName = list[0].GetType().Name; // 表名赋值
            var propertys = list[0].GetType().GetProperties();
            foreach (var pi in propertys)
            {
                var colType = pi.PropertyType;
                if (colType.IsGenericType && colType.GetGenericTypeDefinition() == typeof(Nullable<>)) colType = colType.GetGenericArguments()[0];
                if (IsIgnoreColumn(pi))
                    continue;
                result.Columns.Add(pi.Name, colType);
            }

            for (var i = 0; i < list.Count; i++)
            {
                ArrayList tempList = new();
                foreach (var pi in propertys)
                {
                    if (IsIgnoreColumn(pi))
                        continue;
                    var obj = pi.GetValue(list[i], null);
                    tempList.Add(obj);
                }

                var array = tempList.ToArray();
                result.LoadDataRow(array, true);
            }
        }

        return result;
    }

    /// <summary>
    ///     对象序列化成Json字符串
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static string ToJson(this object? obj)
    {
        return obj == null ? "" : JSON.GetJsonSerializer().Serialize(obj);
    }

    /// <summary>
    ///     Json字符串反序列化成对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="json"></param>
    /// <returns></returns>
    public static T ToObject<T>(this string json)
    {
        return JSON.GetJsonSerializer().Deserialize<T>(json);
    }

    /// <summary>
    ///     排除SqlSugar忽略的列
    /// </summary>
    /// <param name="pi"></param>
    /// <returns></returns>
    private static bool IsIgnoreColumn(PropertyInfo pi)
    {
        var sc = pi.GetCustomAttributes<SugarColumn>(false).FirstOrDefault(u => u.IsIgnore);
        return sc != null;
    }

    /// <summary>
    ///     将内容转换成文件
    /// </summary>
    /// <param name="text"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    public static IFormFile ConvertToIFormFile(string text, string fileName)
    {
        var textBytes = Encoding.UTF8.GetBytes(text);

        // 创建一个临时文件路径
        var tempFilePath = Path.GetTempFileName();

        // 将文本内容写入临时文件
        File.WriteAllBytes(tempFilePath, textBytes);

        // 打开临时文件流
        var fileStream = new FileStream(tempFilePath, FileMode.Open);

        // 创建一个自定义的实现了IFormFile接口的类
        var formFile = new FormFile(fileStream, 0, textBytes.Length, "file", fileName)
        {
            Headers = new HeaderDictionary(),
            ContentType = "text/plain"
        };

        return formFile;
    }

    /// <summary>
    ///     压缩数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public static byte[] Compress(this string input)
    {
        var inputBytes = Encoding.UTF8.GetBytes(input);
        using var outputStream = new MemoryStream();
        using (var gzipStream = new GZipStream(outputStream, CompressionMode.Compress))
        {
            gzipStream.Write(inputBytes, 0, inputBytes.Length);
        }

        return outputStream.ToArray();
    }

    /// <summary>
    ///     解压缩数据
    /// </summary>
    /// <param name="inputBytes"></param>
    /// <returns></returns>
    public static string Decompress(this byte[] inputBytes)
    {
        using var inputStream = new MemoryStream(inputBytes);
        using var outputStream = new MemoryStream();
        using (var gzipStream = new GZipStream(inputStream, CompressionMode.Decompress))
        {
            gzipStream.CopyTo(outputStream);
        }

        var outputBytes = outputStream.ToArray();
        return Encoding.UTF8.GetString(outputBytes);
    }
}