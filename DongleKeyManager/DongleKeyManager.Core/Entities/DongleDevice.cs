using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace DongleKeyManager.Core.Entities;

/// <summary>
/// 看门狗硬件设备实体
/// </summary>
[SugarTable("DongleDevices")]
public class DongleDevice : BaseEntityWithUser
{

    /// <summary>
    /// 设备序列号
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    [Required(ErrorMessage = "设备序列号不能为空")]
    [StringLength(100, ErrorMessage = "设备序列号长度不能超过100个字符")]
    public string SerialNumber { get; set; } = string.Empty;

    /// <summary>
    /// 硬件唯一标识号
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = false)]
    [Required(ErrorMessage = "硬件唯一标识号不能为空")]
    [StringLength(200, ErrorMessage = "硬件唯一标识号长度不能超过200个字符")]
    public string HardwareUniqueId { get; set; } = string.Empty;

    /// <summary>
    /// 加密字符串（基于硬件唯一标识号的MD5加密）
    /// </summary>
    [SugarColumn(Length = 32, IsNullable = false)]
    public string EncryptedString { get; set; } = string.Empty;

    /// <summary>
    /// 设备状态（0-未激活，1-已激活，2-已禁用，3-已过期）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DeviceStatus Status { get; set; } = DeviceStatus.Inactive;

    /// <summary>
    /// 设备名称
    /// </summary>
    [SugarColumn(Length = 200, IsNullable = true)]
    [StringLength(200, ErrorMessage = "设备名称长度不能超过200个字符")]
    public string? DeviceName { get; set; }

    /// <summary>
    /// 设备描述
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    [StringLength(500, ErrorMessage = "设备描述长度不能超过500个字符")]
    public string? Description { get; set; }



    /// <summary>
    /// 激活时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? ActivatedTime { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? ExpiryTime { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? LastUsedTime { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true)]
    [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 设备状态枚举
/// </summary>
public enum DeviceStatus
{
    /// <summary>
    /// 未激活
    /// </summary>
    Inactive = 0,

    /// <summary>
    /// 已激活
    /// </summary>
    Active = 1,

    /// <summary>
    /// 已禁用
    /// </summary>
    Disabled = 2,

    /// <summary>
    /// 已过期
    /// </summary>
    Expired = 3
}
