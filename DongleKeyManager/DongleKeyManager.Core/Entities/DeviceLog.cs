using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace DongleKeyManager.Core.Entities;

/// <summary>
/// 设备使用记录实体
/// </summary>
[SugarTable("DeviceLogs")]
public class DeviceLog : BaseEntity
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public long DeviceId { get; set; }

    /// <summary>
    /// 操作类型（0-查询，1-激活，2-禁用，3-更新，4-删除，5-验证）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public LogOperationType OperationType { get; set; }

    /// <summary>
    /// 操作描述
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    [StringLength(500, ErrorMessage = "操作描述长度不能超过500个字符")]
    public string? OperationDescription { get; set; }

    /// <summary>
    /// 操作用户ID
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? UserId { get; set; }

    /// <summary>
    /// 操作用户名
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true)]
    [StringLength(50, ErrorMessage = "操作用户名长度不能超过50个字符")]
    public string? Username { get; set; }

    /// <summary>
    /// 操作时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime OperationTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = true)]
    [StringLength(50, ErrorMessage = "IP地址长度不能超过50个字符")]
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// 用户代理信息
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    [StringLength(500, ErrorMessage = "用户代理信息长度不能超过500个字符")]
    public string? UserAgent { get; set; }

    /// <summary>
    /// 操作结果（0-失败，1-成功）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsSuccess { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    [SugarColumn(Length = 1000, IsNullable = true)]
    [StringLength(1000, ErrorMessage = "错误信息长度不能超过1000个字符")]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 请求参数（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "TEXT", IsNullable = true)]
    public string? RequestParameters { get; set; }

    /// <summary>
    /// 响应结果（JSON格式）
    /// </summary>
    [SugarColumn(ColumnDataType = "TEXT", IsNullable = true)]
    public string? ResponseResult { get; set; }

    /// <summary>
    /// 执行耗时（毫秒）
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? ExecutionTime { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 日志操作类型枚举
/// </summary>
public enum LogOperationType
{
    /// <summary>
    /// 查询
    /// </summary>
    Query = 0,

    /// <summary>
    /// 激活
    /// </summary>
    Activate = 1,

    /// <summary>
    /// 禁用
    /// </summary>
    Disable = 2,

    /// <summary>
    /// 更新
    /// </summary>
    Update = 3,

    /// <summary>
    /// 删除
    /// </summary>
    Delete = 4,

    /// <summary>
    /// 验证
    /// </summary>
    Verify = 5,

    /// <summary>
    /// 创建
    /// </summary>
    Create = 6
}
