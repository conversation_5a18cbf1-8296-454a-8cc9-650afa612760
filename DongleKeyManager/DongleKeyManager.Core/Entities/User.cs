using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace DongleKeyManager.Core.Entities;

/// <summary>
/// 用户实体
/// </summary>
[SugarTable("Users")]
public class User : BaseEntity
{

    /// <summary>
    /// 用户名
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    [Required(ErrorMessage = "用户名不能为空")]
    [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码哈希
    /// </summary>
    [SugarColumn(Length = 256, IsNullable = false)]
    [Required(ErrorMessage = "密码不能为空")]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// 密码盐值
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string Salt { get; set; } = string.Empty;

    /// <summary>
    /// 用户角色（0-普通用户，1-管理员，2-超级管理员）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public UserRole Role { get; set; } = UserRole.User;

    /// <summary>
    /// 用户状态（0-正常，1-禁用，2-锁定）
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public UserStatus Status { get; set; } = UserStatus.Normal;

    /// <summary>
    /// 最后登录时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? LastLoginTime { get; set; }

    /// <summary>
    /// 登录次数
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int LoginCount { get; set; } = 0;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remarks { get; set; }
}

/// <summary>
/// 用户角色枚举
/// </summary>
public enum UserRole
{
    /// <summary>
    /// 普通用户
    /// </summary>
    User = 0,

    /// <summary>
    /// 管理员
    /// </summary>
    Admin = 1,

    /// <summary>
    /// 超级管理员
    /// </summary>
    SuperAdmin = 2
}

/// <summary>
/// 用户状态枚举
/// </summary>
public enum UserStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 0,

    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 1,

    /// <summary>
    /// 锁定
    /// </summary>
    Locked = 2
}
