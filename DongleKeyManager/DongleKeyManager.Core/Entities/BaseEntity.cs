using SqlSugar;

namespace DongleKeyManager.Core.Entities;

/// <summary>
/// 基础实体类
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public long Id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreatedTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public bool IsDeleted { get; set; } = false;
}

/// <summary>
/// 带操作用户信息的基础实体类
/// </summary>
public abstract class BaseEntityWithUser : BaseEntity
{
    /// <summary>
    /// 创建用户ID
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? CreatedUserId { get; set; }

    /// <summary>
    /// 更新用户ID
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? UpdatedUserId { get; set; }
}
