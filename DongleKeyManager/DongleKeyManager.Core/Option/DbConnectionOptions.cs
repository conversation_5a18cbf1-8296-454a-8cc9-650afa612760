using System.ComponentModel.DataAnnotations;
using DongleKeyManager.Core.Const;
using Furion.ConfigurableOptions;
using Microsoft.Extensions.Configuration;
using SqlSugar;

namespace Feng.IotGateway.Core.Option;

/// <summary>
///     数据库配置选项
/// </summary>
public sealed class DbConnectionOptions : IConfigurableOptions<DbConnectionOptions>
{
    /// <summary>
    ///     启用控制台打印SQL
    /// </summary>
    public bool EnableConsoleSql { get; set; }

    /// <summary>
    ///     数据库集合
    /// </summary>
    public List<DbConnectionConfig> ConnectionConfigs { get; set; }

    public void PostConfigure(DbConnectionOptions options, IConfiguration configuration)
    {
        foreach (var dbConfig in options.ConnectionConfigs)
            if (dbConfig.ConfigId == null || string.IsNullOrWhiteSpace(dbConfig.ConfigId.ToString()))
                dbConfig.ConfigId = SqlSugarConst.Default;
    }
}

/// <summary>
///     数据库连接配置
/// </summary>
public sealed class DbConnectionConfig : ConnectionConfig
{
    /// <summary>
    ///     启用表初始化
    /// </summary>
    [Range(0, 2)]
    public bool EnableInitTable { get; set; }

    /// <summary>
    ///     启用种子初始化
    /// </summary>
    public bool EnableInitSeed { get; set; }
}