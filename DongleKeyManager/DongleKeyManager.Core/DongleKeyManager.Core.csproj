<Project Sdk="Microsoft.NET.Sdk">


    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <DocumentationFile>DongleKeyManager.Core.xml</DocumentationFile>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="DongleKeyManager.Core.xml"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="4.9.7.106"/>
        <PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="4.9.7.106"/>
        <PackageReference Include="Furion.Pure" Version="4.9.7.106"/>
        <PackageReference Include="SqlSugarCore" Version="5.1.4.198"/>
        <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
    </ItemGroup>

</Project>
