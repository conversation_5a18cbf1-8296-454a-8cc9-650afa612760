using DongleKeyManager.Core.Entities;
using SqlSugar;
using System.Security.Cryptography;

namespace DongleKeyManager.Core.Database;

/// <summary>
/// 数据库初始化器
/// </summary>
public static class DatabaseInitializer
{
    /// <summary>
    /// 初始化数据库
    /// </summary>
    public static async Task InitializeAsync()
    {
        try
        {
            // 创建数据库（如果不存在）
            DbContext.Instance.DbMaintenance.CreateDatabase();

            // 创建表结构
            CreateTables();

            // 初始化基础数据
            await InitializeDataAsync();

            Console.WriteLine("数据库初始化完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"数据库初始化失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 创建表结构
    /// </summary>
    private static void CreateTables()
    {
        // 创建用户表
        DbContext.Instance.CodeFirst.InitTables<User>();

        // 创建设备表
        DbContext.Instance.CodeFirst.InitTables<DongleDevice>();

        // 创建设备日志表
        DbContext.Instance.CodeFirst.InitTables<DeviceLog>();

        Console.WriteLine("数据库表结构创建完成");
    }

    /// <summary>
    /// 初始化基础数据
    /// </summary>
    private static async Task InitializeDataAsync()
    {
        // 检查是否已存在超级管理员
        var existsAdmin = await DbContext.Instance.Queryable<User>()
            .Where(x => x.Role == UserRole.SuperAdmin && !x.IsDeleted)
            .AnyAsync();

        if (!existsAdmin)
        {
            // 创建默认超级管理员
            await CreateDefaultAdminAsync();
        }

        // 可以在这里添加其他初始化数据
        Console.WriteLine("基础数据初始化完成");
    }

    /// <summary>
    /// 创建默认超级管理员
    /// </summary>
    private static async Task CreateDefaultAdminAsync()
    {
        // 生成盐值和密码哈希
        var salt = GenerateSalt();
        var passwordHash = GeneratePasswordHash("admin123", salt);

        var admin = new User
        {
            Username = "admin",
            PasswordHash = passwordHash,
            Salt = salt,
            RealName = "系统管理员",
            Role = UserRole.SuperAdmin,
            Status = UserStatus.Normal,
            CreatedTime = DateTime.Now,
            Remarks = "系统默认超级管理员账户，默认密码：admin123"
        };

        await DbContext.Instance.Insertable(admin).ExecuteCommandAsync();
        Console.WriteLine("默认超级管理员创建完成 - 用户名: admin, 密码: admin123");
    }

    /// <summary>
    /// 生成随机盐值
    /// </summary>
    private static string GenerateSalt(int length = 16)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var random = new Random();
        var salt = new char[length];

        for (int i = 0; i < length; i++)
        {
            salt[i] = chars[random.Next(chars.Length)];
        }

        return new string(salt);
    }

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    private static string GeneratePasswordHash(string password, string salt)
    {
        var saltedPassword = password + salt;
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var inputBytes = System.Text.Encoding.UTF8.GetBytes(saltedPassword);
        var hashBytes = sha256.ComputeHash(inputBytes);

        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    public static async Task<bool> CheckConnectionAsync()
    {
        try
        {
            await DbContext.Instance.Ado.GetDataTableAsync("SELECT 1");
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    public static async Task<DatabaseInfo> GetDatabaseInfoAsync()
    {
        try
        {
            var userCount = await DbContext.Instance.Queryable<User>()
                .Where(x => !x.IsDeleted)
                .CountAsync();

            var deviceCount = await DbContext.Instance.Queryable<DongleDevice>()
                .Where(x => !x.IsDeleted)
                .CountAsync();

            var logCount = await DbContext.Instance.Queryable<DeviceLog>()
                .CountAsync();

            return new DatabaseInfo
            {
                IsConnected = true,
                UserCount = userCount,
                DeviceCount = deviceCount,
                LogCount = logCount,
                DatabaseType = "SQLite",
                InitializedTime = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            return new DatabaseInfo
            {
                IsConnected = false,
                ErrorMessage = ex.Message
            };
        }
    }
}

/// <summary>
/// 数据库信息
/// </summary>
public class DatabaseInfo
{
    /// <summary>
    /// 是否连接成功
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 用户数量
    /// </summary>
    public int UserCount { get; set; }

    /// <summary>
    /// 设备数量
    /// </summary>
    public int DeviceCount { get; set; }

    /// <summary>
    /// 日志数量
    /// </summary>
    public int LogCount { get; set; }

    /// <summary>
    /// 数据库类型
    /// </summary>
    public string DatabaseType { get; set; } = string.Empty;

    /// <summary>
    /// 初始化时间
    /// </summary>
    public DateTime InitializedTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
