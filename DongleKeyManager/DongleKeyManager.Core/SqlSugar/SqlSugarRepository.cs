using Furion;
using SqlSugar;

namespace DongleKeyManager.Core.SqlSugar;

/// <summary>
///     SqlSugar仓储类
/// </summary>
/// <typeparam name="T"></typeparam>
public class SqlSugarRepository<T> : SimpleClient<T> where T : class, new()
{
    protected ITenant iTenant; // 多租户事务

    public SqlSugarRepository(ISqlSugarClient context = null) : base(context) // 默认值等于null不能少
    {
        iTenant = App.GetService<ISqlSugarClient>().AsTenant();
        Context = iTenant.GetConnectionWithAttr<T>();
    }
}