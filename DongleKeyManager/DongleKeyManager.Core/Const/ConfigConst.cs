using System.Collections.Concurrent;

namespace Feng.IotGateway.Core.Const;

/// <summary>
/// 配置常量
/// </summary>
public class ConfigConst
{
    /// <summary>
    /// flink服务器ip
    /// </summary>
    public const string SysFLinkIp = "sys_fLinkIp";

    /// <summary>
    /// flink服务器端口
    /// </summary>
    public const string SysFLinkPort = "sys_fLinkPort";

    /// <summary>
    /// flink服务器是否启动
    /// </summary>
    public const string SysFLinkOpen = "sys_fLinkOpen";

    /// <summary>
    /// UTC时区
    /// </summary>
    public const string UtcTime = "sys_utcTime";

    /// <summary>
    /// 网关采集是否授权
    /// </summary>
    public const string Authorization = "sys_authorization";

    /// <summary>
    /// 缓存最大数量
    /// </summary>
    public const string MaxOffLine = "sys_maxOffLine";

    /// <summary>
    /// 默认加载平台logo
    /// </summary>
    public const string Platform = "sys_platform";

    /// <summary>
    /// 是否保存已发送数据
    /// </summary>
    public const string SaveData = "sys_saveData";

    /// <summary>
    /// dnc文件存储路径
    /// </summary>
    public const string DncFilePath = "sys_dncFilePath";

    /// <summary>
    /// 是否允许设备写入
    /// </summary>
    public const string WhetherToAllowTheDeviceToWrite = "WhetherToAllowTheDeviceToWrite";

    /// <summary>
    ///     系统默认解锁密码
    /// </summary>
    public const string SysUnLockPassword = "sys_unLock_password";

    /// <summary>
    ///     系统默认主页
    /// </summary>
    public const string SysDefIndexPage = "sys_def_indexPage";

    /// <summary>
    ///     外部链接
    /// </summary>
    public const string SysExternalLink = "sys_external_link";
}

/// <summary>
/// 全局配置管理器 - 使用静态字典替代缓存机制
/// </summary>
public static class GlobalConfigManager
{
    /// <summary>
    /// 全局配置字典，线程安全
    /// </summary>
    private static readonly ConcurrentDictionary<string, string> _configDictionary = new();

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <param name="code">配置编码</param>
    /// <returns>配置值</returns>
    public static string? GetConfigValue(string code)
    {
        if (string.IsNullOrWhiteSpace(code)) return null;
        return _configDictionary.TryGetValue(code, out var value) ? value : null;
    }

    /// <summary>
    /// 获取配置值并转换为指定类型
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="code">配置编码</param>
    /// <returns>转换后的配置值</returns>
    public static T? GetConfigValue<T>(string code)
    {
        var value = GetConfigValue(code);
        if (string.IsNullOrWhiteSpace(value)) return default;

        try
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <param name="code">配置编码</param>
    /// <param name="value">配置值</param>
    public static void SetConfigValue(string code, string? value)
    {
        if (string.IsNullOrWhiteSpace(code)) return;

        if (value == null)
        {
            _configDictionary.TryRemove(code, out _);
        }
        else
        {
            _configDictionary.AddOrUpdate(code, value, (key, oldValue) => value);
        }
    }

    /// <summary>
    /// 批量设置配置值
    /// </summary>
    /// <param name="configs">配置字典</param>
    public static void SetConfigValues(Dictionary<string, string?> configs)
    {
        foreach (var config in configs)
        {
            SetConfigValue(config.Key, config.Value);
        }
    }

    /// <summary>
    /// 检查配置是否存在
    /// </summary>
    /// <param name="code">配置编码</param>
    /// <returns>是否存在</returns>
    public static bool ContainsConfig(string code)
    {
        return !string.IsNullOrWhiteSpace(code) && _configDictionary.ContainsKey(code);
    }

    /// <summary>
    /// 获取所有配置
    /// </summary>
    /// <returns>所有配置的副本</returns>
    public static Dictionary<string, string> GetAllConfigs()
    {
        return new Dictionary<string, string>(_configDictionary);
    }

    /// <summary>
    /// 清空所有配置
    /// </summary>
    public static void ClearAllConfigs()
    {
        _configDictionary.Clear();
    }
}