namespace Feng.IotGateway.Core.Const;

/// <summary>
/// 网关中各文件存放目录
/// </summary>
public class GatewayFilePath
{
    /// <summary>
    /// sn存放地址
    /// </summary>
    public const string SnPath = "/etc/DeviceConf/sn.txt";
    
    /// <summary>
    ///     加密授权路径
    /// </summary>
    public static readonly string KeyPath = "/etc/DeviceConf/key.txt";

    /// <summary>
    ///     rsa解密文件路径
    /// </summary>
    public static readonly string RsaPath = "/etc/DeviceConf/RSA.Private";

    /// <summary>
    ///     网关名称标识
    /// </summary>
    public static readonly string IdentPath = "/etc/DeviceConf/Ident.txt";
    
    /// <summary>
    ///     默认路由地址
    /// </summary>
    public static readonly string DefRoutePath = "/etc/DeviceConf/net_select";
    
    /// <summary>
    /// ubuntu 网络配置文件
    /// </summary>
    public static readonly string UbuntuNetworkPath = "/etc/netplan/01-network-manager-all.yaml";
}