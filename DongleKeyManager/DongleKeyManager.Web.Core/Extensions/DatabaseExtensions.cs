using DongleKeyManager.Core.SqlSugar;
using Microsoft.Extensions.DependencyInjection;

namespace DongleKeyManager.Web.Core.Extensions;

/// <summary>
///     数据库扩展方法
/// </summary>
public static class DatabaseExtensions
{
    /// <summary>
    ///     添加数据库初始化服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddDatabaseInitialization(this IServiceCollection services)
    {
        // 初始化数据库上下文
        services.AddSqlSugar();
        return services;
    }
}