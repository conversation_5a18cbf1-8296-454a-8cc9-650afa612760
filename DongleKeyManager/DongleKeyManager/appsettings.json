{"$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json", "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "DbConnection": {"EnableConsoleSql": false, "ConnectionConfigs": [{"ConfigId": "1300000000000", "DbType": "Sqlite", "ConnectionString": "Data Source=/Edge/Data/IotGateway.db", "EnableInitTable": true, "EnableInitSeed": true}]}}