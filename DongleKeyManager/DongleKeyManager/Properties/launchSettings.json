{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:53785", "sslPort": 44342}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "DongleKeyManager.Web.Entry": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}